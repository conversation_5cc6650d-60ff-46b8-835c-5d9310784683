import { Checkbox } from '@mui/material'
import { format, parse } from 'date-fns'
import { PlanningStage } from 'entities/shared/common.entities.ts'
import { ModalReportProtocol } from 'features/ModalReportProtocol'
import { ItemValue } from 'pages/ReportsPage/ui/ModalCreateEditReport/config/types.ts'
import { IRow, IValues } from 'pages/ReportsPage/ui/ModalCreateEditReport/ui/ModalCreateEditReport.tsx'
import { Values } from 'pages/ReportsPage/ui/ModalUnloadingAvrchmSummary/model/types.ts'
import { ChangeEvent, Dispatch, SetStateAction, useEffect, useMemo, useRef, useState } from 'react'
import api from 'shared/api/index.ts'
import { ValidateMailingParams } from 'shared/api/reportsManager/reportsManager.ts'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import {
  IRow as IArchmRow,
  IStageList,
  prepareReportAvrchmDataTable,
} from 'shared/lib/prepareReportDataTable/prepareReportAvrchmDataTable.ts'
import { AutocompleteEmails, AutocompleteEmailsProps, Loader } from 'shared/ui'
import { Button } from 'shared/ui/Button/index.ts'
import { DatePicker } from 'shared/ui/DatePicker/index.ts'
import { LoadingButton } from 'shared/ui/LoadingButton/index.ts'
import { Modal } from 'shared/ui/Modal/index.ts'
import { ModalWarning } from 'shared/ui/ModalWarning/ModalWarning.tsx'
import { Select } from 'shared/ui/Select/index.ts'
import { Switch } from 'shared/ui/Switch'
import { TextField } from 'shared/ui/TextField/index.ts'
import { TextFieldWithPrompt } from 'shared/ui/TextFieldWithPrompt/TextFieldWithPrompt.tsx'
import { useStore } from 'stores/useStore.ts'
import { Table } from 'widgets/Table'

import { INITIAL_VALUES } from './config/const.ts'
import { validation } from './lib/validation.ts'
import cls from './ModalMailingReportAvrchmSummary.module.scss'

interface Props {
  reportType: string
  id: number
  onClose: VoidFunction
}

export const ModalMailingReportAvrchmSummary = (props: Props) => {
  const { reportType, id, onClose } = props
  const { reportsStore, notificationStore } = useStore()
  const {
    namePlaceholders,
    mailBodyPlaceholders,
    typesMailingAttachment,
    fetchNamePlaceholders,
    fetchMailingAttachment,
    validateMailing,
    validateData,
  } = reportsStore

  const [values, setValues] = useState<IValues>(INITIAL_VALUES as unknown as IValues)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [isLoadingValues, setIsLoadingValues] = useState(false)
  const [isVisibleModalProtocol, setIsVisibleModalProtocol] = useState(false)
  const refTableBlock = useRef<HTMLDivElement>(null)
  const [rows, setRows] = useState<IRow[]>([])
  const [stagesList, setStagesList] = useState<IStageList[]>([])
  const fetchReport = async (id: number) => {
    try {
      setIsLoadingValues(true)
      const res = await api.reportsManager.getReport(id)

      setValues(
        (prev) =>
          ({
            ...prev,
            name: res.name,
            plants: res?.settings?.plants,
            avrcmTes: res?.settings?.avrcmTes ?? false,
            fileNameTemplate: res?.settings?.fileNameTemplate,
            templatePath: {
              oldValue: res?.settings?.templatePath,
              newValue: res?.settings?.templatePath,
            },
            mailingReportDirectory: {
              oldValue: res?.settings?.unloadingDirectory,
              newValue: res?.settings?.unloadingDirectory,
            },
            mailSubject: res?.settings?.mailSubject,
            mailingAttachment: res?.settings?.defaultMailingAttachment,
            emails: res?.settings?.emails,
            messageBody: {
              oldValue: res?.settings?.messageBody,
              newValue: res?.settings?.messageBody,
            },
            summaryFormat: res?.settings?.summaryFormat ?? false,
            putHeader: res?.settings?.putHeader ?? false,
          }) as IValues,
      )
    } catch (error) {
      console.log(error)
    } finally {
      setIsLoadingValues(false)
    }
  }

  const fetchStages = async (date: Date) => {
    const calcDates = [format(date, 'yyyy-MM-dd')]
    try {
      const res = await api.calculationsManager.getListStages({
        calcDates,
      })
      prepareReportAvrchmDataTable(
        res.dateStages,
        setStagesList,
        setRows as Dispatch<SetStateAction<IArchmRow[]>>,
        setValues as Dispatch<SetStateAction<Values>>,
      )
    } catch (error) {
      console.log(error)
    }
  }

  const fetchUnloadingDates = async (id: number) => {
    try {
      const res = await api.reportsManager.getUnloadingDates(id)
      prepareReportAvrchmDataTable(
        res.dateStages,
        setStagesList,
        setRows as Dispatch<SetStateAction<IArchmRow[]>>,
        setValues as Dispatch<SetStateAction<Values>>,
      )
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    const getData = async () => {
      try {
        setIsLoadingValues(true)

        await Promise.all([fetchMailingAttachment(), fetchNamePlaceholders()])
        await Promise.all([fetchReport(id), fetchUnloadingDates(id)])
      } catch (error) {
        console.log(error)
      }
    }

    getData()

    return () => {
      reportsStore.validateData = { warnings: [], errors: [], result: '' }
    }
  }, [])

  const [validationInProgress, setValidationInProgress] = useState(false)

  const handleValidateMailing = async () => {
    const [isValid, errors] = validation(values)
    setErrors(errors)
    if (!isValid) return

    const calculationRequest = {
      targetDate: format(parse(rows[0]?.date ?? '', 'dd.MM.yyyy', new Date()), 'yyyy-MM-dd'),
      planingStage: rows[0].planingStage,
    }

    const params = {
      reportId: id,
      reportType,
      mailingParameters: {
        calculationRequest,
        mailingAttachment: values.mailingAttachment,
        fileNameTemplate: values.fileNameTemplate,
        templatePath: values?.templatePath?.newValue,
        mailingReportDirectory: values?.mailingReportDirectory?.newValue,
        mailSubject: values.mailSubject,
        emails: values.emails,
        avrcmTes: values.avrcmTes,
        messageBody: values?.messageBody?.newValue,
        summaryFormat: values.summaryFormat,
        putHeader: values.putHeader,
      },
    }

    try {
      setValidationInProgress(true)
      const data = await validateMailing(params as ValidateMailingParams)

      if (data?.errors?.length || data?.warnings?.length) {
        setIsVisibleModalProtocol(true)
      } else if (!data?.errors?.length && !data?.warnings?.length) {
        handleMailing()
      }
    } catch (error) {
      console.log('error', error)
    } finally {
      setValidationInProgress(false)
    }
  }

  const mailingReportAbortControllerRef = useRef<AbortController | null>(null)

  const [isMailingReport, setIsMailingReport] = useState(false)
  const [reportMailingName, setReportMailingName] = useState('')

  const handleMailing = async () => {
    mailingReportAbortControllerRef.current?.abort()

    mailingReportAbortControllerRef.current = new AbortController()

    const calculationRequest = {
      targetDate: format(parse(rows[0]?.date ?? '', 'dd.MM.yyyy', new Date()), 'yyyy-MM-dd'),
      planingStage: rows[0].planingStage,
    }

    const params = {
      reportId: id,
      reportType,
      mailingParameters: {
        calculationRequest,
        mailingAttachment: values.mailingAttachment,
        fileNameTemplate: values.fileNameTemplate,
        templatePath: values?.templatePath?.newValue,
        mailingReportDirectory: values?.mailingReportDirectory?.newValue,
        mailSubject: values?.mailSubject,
        emails: values?.emails,
        avrcmTes: values?.avrcmTes,
        messageBody: values?.messageBody?.newValue,
        summaryFormat: values.summaryFormat,
        putHeader: values.putHeader,
      },
    }

    const reportName =
      values?.fileNameTemplate && values?.date
        ? values?.fileNameTemplate
            .replace(/<дата>/i, format(values?.date, 'dd.MM.yyyy'))
            .replace(/<эп>/i, stagesList.find((item) => item?.value === rows[0]?.planingStage)?.label as string)
        : ''
    setReportMailingName(reportName)

    try {
      setIsMailingReport(true)
      setIsVisibleModalProtocol(false)

      const unloadingResponse = await api.reportsManager.mailingReport(
        params as ValidateMailingParams,
        mailingReportAbortControllerRef.current.signal,
      )

      if (unloadingResponse.errors.length) {
        notificationStore.addNotification({
          title: 'Ошибка',
          description: unloadingResponse?.result ?? '',
          type: 'warning',
        })

        reportsStore.validateData = unloadingResponse
        setIsVisibleModalProtocol(true)
      } else {
        notificationStore.addNotification({
          title: '',
          description: unloadingResponse?.result ?? '',
          type: 'success',
        })

        onClose()
      }
    } catch (error) {
      console.log(error)
    } finally {
      setIsMailingReport(false)
      setIsVisibleModalWarning(false)
    }
  }

  //
  // change values

  const handleChangeValue = (key: string, value: ItemValue) => {
    const keyValue =
      value === 'EXISTING_REPORT' ? 'templatePath' : value === 'NEW_REPORT' ? 'mailingReportDirectory' : null
    switch (key) {
      case 'templatePath':
      case 'messageBody':
      case 'mailingReportDirectory':
        setValues((prev) => ({
          ...prev,
          [key]: {
            ...prev[key],
            newValue: value,
          },
        }))
        break
      case 'mailingAttachment':
        setValues((prev) => {
          const k = keyValue ?? null
          if (k) {
            const p = prev[k] ? prev[k] : {}

            return {
              ...prev,
              [key]: value,
              [keyValue as string]: {
                ...p,
                newValue: p?.oldValue ?? '',
              },
            } as IValues
          } else {
            return prev as IValues
          }
        })
        break
      default:
        setValues((prev) => {
          const newValues = { ...prev, [key]: value }

          // При выключении "По форме Свод" автоматически выключаем "Выгружать шапку"
          if (key === 'summaryFormat' && !value) {
            newValues.putHeader = false
          }

          return newValues
        })
        break
    }

    if (errors[key] !== undefined)
      setErrors((prev) => {
        const newObj = { ...prev }
        delete newObj[key]

        return newObj
      })

    reportsStore.validateData = { warnings: [], errors: [], result: '' }
  }

  const handleChangeDate = (value: Date) => {
    setValues((prev) => ({
      ...prev,
      date: value,
    }))
    reportsStore.validateData = { warnings: [], errors: [], result: '' }

    fetchStages(value)
  }

  const columns = [
    {
      name: 'date',
      title: 'Дата',
      width: 200,
    },
    {
      name: 'planingStage',
      title: 'Этап планирования',
      width: 200,
      editingEnabled: true,
      editType: 'select',
      selectDataFromRow: 'stages',
      render: (value: PlanningStage, row: IRow) => {
        const currentItem = row.stages.find((el) => el.value === value)

        return <>{currentItem?.label ?? ''}</>
      },
    },
  ]

  const heightTable = useMemo(() => {
    return refTableBlock.current ? refTableBlock.current.clientHeight : 55
  }, [refTableBlock?.current?.clientHeight])

  //table
  //

  const [isVisibleModalWarning, setIsVisibleModalWarning] = useState(false)

  const handleClose = () => {
    if (isMailingReport) {
      setIsVisibleModalWarning(true)
    } else {
      onClose()
    }
  }

  const handleResetMailing = () => {
    mailingReportAbortControllerRef.current?.abort()

    setIsVisibleModalWarning(false)
    onClose()
  }

  const handleEmailsError: AutocompleteEmailsProps['onError'] = (error) => {
    setErrors((prev) => {
      const newErrors = { ...prev }
      if (!error) {
        delete newErrors.emails

        return newErrors
      }

      return {
        ...newErrors,
        emails: error,
      }
    })
  }

  return (
    <Modal
      open
      title='Рассылка отчёта сводных данных АВРЧМ'
      maxWidth='md'
      onClose={handleClose}
      skipConfirmOnClose
      actions={
        <div className={cls.modalFooter}>
          <div className={cls.modalFooterRight}>
            {validateData.errors.length > 0 && (
              <Button
                variant='outlined'
                onClick={() => setIsVisibleModalProtocol(true)}
                disabled={isLoadingValues || isMailingReport}
                className={cls.btnProtocol}
              >
                Протокол рассылки
              </Button>
            )}
            <Button variant='outlined' onClick={handleClose} disabled={isMailingReport}>
              Отменить
            </Button>
            <LoadingButton
              variant='contained'
              onClick={handleValidateMailing}
              disabled={validateData.errors.length > 0 || isLoadingValues}
              loading={validationInProgress || isMailingReport}
              className={cls.sendButton}
            >
              Отправить
            </LoadingButton>
          </div>
        </div>
      }
    >
      <>
        <div
          className={classNames(
            cls.wrapper,
            {
              [cls.wrapperLoading]: isLoadingValues || isMailingReport,
            },
            [],
          )}
        >
          {isLoadingValues || isMailingReport ? (
            <Loader />
          ) : (
            <>
              <div className={cls.row}>
                <div className={cls.labelRow}>Название</div>
                <div className={cls.valueBlock}>
                  <TextField
                    className={cls.valueBlock}
                    value={values?.name}
                    type='string'
                    disabled
                    onChange={(e) => handleChangeValue('name', e.target.value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Дата (план)</div>
                <div className={cls.valueBlock}>
                  <DatePicker className={cls.valueBlockShort} value={values.date as Date} setValue={handleChangeDate} />
                </div>
              </div>
              <div ref={refTableBlock} className={cls.rowTable}>
                <Table
                  columns={columns}
                  rows={rows}
                  setRows={setRows}
                  height={heightTable}
                  editMode
                  columnSearchDisabled={['date', 'planingStage']}
                />
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Вложение</div>
                <div className={`${cls.valueBlock} ${cls.valueBlockShort}`}>
                  <Select
                    variant='outlined'
                    items={typesMailingAttachment}
                    value={values.mailingAttachment}
                    onChange={(value) => handleChangeValue('mailingAttachment', value)}
                    className={cls.selectFontSize}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>
                  <Switch
                    checked={values.summaryFormat}
                    onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) =>
                      handleChangeValue('summaryFormat', checked)
                    }
                    disabled={values.mailingAttachment === 'EXISTING_REPORT'}
                    label='По форме Свод'
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>
                  <Switch
                    checked={values.putHeader}
                    onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) =>
                      handleChangeValue('putHeader', checked)
                    }
                    disabled={!values.summaryFormat || values.mailingAttachment === 'EXISTING_REPORT'}
                    label='Выгружать шапку'
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Станции</div>
                <div className={cls.valueBlock}>
                  <TextField
                    value={values?.plants?.map((item) => item.name).join(', ')}
                    type='string'
                    multiline
                    className={cls.textFieldInput}
                    disabled
                    onChange={(e) => handleChangeValue('name', e.target.value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>АВРЧМ ТЭС</div>
                <div className={cls.valueBlockFullWidth}>
                  <Checkbox
                    checked={values.avrcmTes}
                    disabled={values.summaryFormat || values.mailingAttachment === 'EXISTING_REPORT'}
                    onChange={(e: ChangeEvent<HTMLInputElement>) => handleChangeValue('avrcmTes', e.target.checked)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Шаблон названия файла</div>
                <div className={cls.valueBlock}>
                  <TextFieldWithPrompt
                    error={!!errors.fileNameTemplate?.length}
                    helperText={errors.fileNameTemplate ?? ''}
                    className={cls.textFieldPropmt}
                    value={values.fileNameTemplate}
                    items={namePlaceholders}
                    onChange={(value: string) => handleChangeValue('fileNameTemplate', value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Путь к шаблону</div>
                <div className={cls.valueBlock}>
                  <TextField
                    value={values.templatePath?.newValue}
                    disabled={values.mailingAttachment === 'EXISTING_REPORT'}
                    type='string'
                    multiline
                    className={cls.textFieldInput}
                    onChange={(e) => handleChangeValue('templatePath', e.target.value)}
                    placeholder='\\192.168.10.10\neptune\file.xls'
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Директория к отчёту</div>
                <div className={cls.valueBlock}>
                  <TextField
                    value={values.mailingReportDirectory?.newValue}
                    disabled={values.mailingAttachment === 'NEW_REPORT'}
                    type='string'
                    className={cls.textFieldInput}
                    onChange={(e) => handleChangeValue('mailingReportDirectory', e.target.value)}
                    placeholder='\\192.168.10.10\neptune'
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Тема письма</div>
                <div className={cls.valueBlock}>
                  <TextFieldWithPrompt
                    error={!!errors.mailSubject?.length}
                    helperText={errors.mailSubject ?? ''}
                    className={cls.textFieldPropmt}
                    value={values.mailSubject}
                    items={namePlaceholders}
                    onChange={(value: string) => handleChangeValue('mailSubject', value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Получатели рассылки</div>
                <div className={cls.valueBlock}>
                  <AutocompleteEmails
                    error={!!errors.emails?.length}
                    value={values.emails as unknown as string[]}
                    onValuesChange={(values) => handleChangeValue('emails', values)}
                    onError={handleEmailsError}
                    className={cls.autocomplite}
                  />
                </div>
              </div>

              <div className={cls.row}>
                <div className={cls.labelRow}>От кого</div>
                <div className={cls.valueBlock}>
                  <TextFieldWithPrompt
                    multiline
                    rows={3}
                    maxLength={255}
                    helperText=''
                    className={cls.textFieldPropmt}
                    value={values?.messageBody?.newValue}
                    items={mailBodyPlaceholders}
                    onChange={(value) => handleChangeValue('messageBody', value)}
                  />
                </div>
              </div>
            </>
          )}
        </div>

        {isVisibleModalProtocol && (
          <ModalReportProtocol
            handleClose={() => setIsVisibleModalProtocol(false)}
            handleAccept={handleMailing}
            acceptText='Отправить'
            closeText='Отменить'
            title='Отправка отчёта'
          />
        )}

        {isVisibleModalWarning && (
          <ModalWarning
            description={`Рассылка отчёта "${reportMailingName}" будет прервана`}
            handleReset={handleResetMailing}
            handleClose={() => setIsVisibleModalWarning(false)}
          />
        )}
      </>
    </Modal>
  )
}
