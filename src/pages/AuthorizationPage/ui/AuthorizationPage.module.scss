.AuthorizationPage {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.Card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 450px;
  width: 450px;
  padding: 30px;
  margin: 0 auto;
  background-color: var(--background-color-secondary);
  box-shadow: 0 5px 18px 12px rgb(34 60 80 / 20%);
  border-radius: 20px;
}

.Title {
  margin: 5px 0;
}

.VersionProject {
  margin-top: 10px;
}

.ButtonLogin {
  width: 100%;
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  background-color: var(--primary-color);

  &:hover {
    background-color: var(--primary-color-hover) !important;
  }

  &:disabled {
    background-color: var(--disabled-button-bg) !important;
  }
}

.DropDown {
  margin: 10px 0 !important;

  // выбранный элемент внутри селекта
  :global(.MuiSelect-select) {
    display: flex;
    align-items: center;
    padding: 0 0 5px 0 !important;
    font-size: 16px;
  }

  // label когда он не shrink
  :global(.MuiInputLabel-root:not(.MuiInputLabel-shrink)) {
    left: -10px !important;
  }

  // label когда он shrink
  :global(.MuiInputLabel-root.MuiInputLabel-shrink) {
    font-size: 16px !important;
    font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif !important;
    left: -14px !important;
  }
}

.TextFieldLogin {
  margin: 10px 0 !important;
  background-color: white !important;
}

.LogoContainer {
  color: var(--primary-color);
}
