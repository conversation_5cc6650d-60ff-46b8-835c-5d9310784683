import { observer } from 'mobx-react-lite'
import { type ChangeEvent, useCallback, useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { connect } from 'shared/lib/axios/axios'
import { classNames } from 'shared/lib/classNames/classNames'
import { AppVersion } from 'shared/ui/AppVersion'
import { Button } from 'shared/ui/Button'
import { Icon } from 'shared/ui/Icon'
import { Select } from 'shared/ui/Select'
import { TextField } from 'shared/ui/TextField'
import { useStore } from 'stores/useStore'

import cls from './AuthorizationPage.module.scss'

interface AuthorizationPageProps {
  className?: string
}

const AuthorizationPage = observer((props: AuthorizationPageProps) => {
  const { className } = props
  const { authStore } = useStore()
  const { domainList, getDomains, login, isAuthProgress, getAppVersion, appVersion } = authStore
  const [values, setValues] = useState({
    domain: '',
    login: '',
    password: '',
  })

  useEffect(() => {
    getDomains()
    getAppVersion()
  }, [])

  const handleSelectChange = (value: string) => {
    setValues((prev) => ({
      ...prev,
      domain: value,
    }))
  }

  const handleInputChange = useCallback((event: ChangeEvent<HTMLInputElement>, key: string) => {
    const { value } = event.target
    setValues((prev) => ({
      ...prev,
      [key]: value,
    }))
  }, [])

  const history = useNavigate()

  const disabled = !values.domain || !values.login || !values.password

  const handleSubmit = useCallback(() => {
    if (!disabled) {
      login(values).then((url: string | boolean) => {
        if (typeof url === 'string') {
          history(url)
          connect()
        }
      })
    }
  }, [values])

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter') {
      handleSubmit()
    }
  }

  return (
    <div
      className={classNames(cls.AuthorizationPage, {}, className ? [className] : [])}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      <div className={classNames(cls.Card, {}, [])}>
        <div className={classNames(cls.LogoContainer, {}, [])}>
          <Icon name='whiteLogo' width={100} height={100} />
        </div>
        <AppVersion className={classNames(cls.VersionProject, {}, [])} appVersion={appVersion} />
        <h2 className={classNames(cls.Title, {}, [])}>НЕПТУН</h2>
        <Select
          className={classNames(cls.DropDown, {}, [])}
          variant='standard'
          label='Выберите домен'
          items={domainList}
          onChange={handleSelectChange}
          value={values.domain}
        />
        <TextField
          label='Логин'
          className={classNames(cls.TextFieldLogin, {}, [])}
          value={values.login}
          onChange={(e: ChangeEvent<HTMLInputElement>) => {
            handleInputChange(e, 'login')
          }}
          icon='user'
          type='text'
          id='login'
        />
        <TextField
          label='Пароль'
          className={classNames(cls.TextFieldLogin, {}, [])}
          value={values.password}
          onChange={(e: ChangeEvent<HTMLInputElement>) => {
            handleInputChange(e, 'password')
          }}
          icon='password'
          type='password'
          id='password'
        />
        <Button
          className={classNames(cls.ButtonLogin, {}, [])}
          size='large'
          onClick={handleSubmit}
          disabled={disabled}
          loading={isAuthProgress}
        >
          Войти
        </Button>
      </div>
    </div>
  )
})

export default AuthorizationPage
