import { observer } from 'mobx-react'
import { useNavigate } from 'react-router-dom'
import { classNames } from 'shared/lib/classNames/classNames'
import { locationParse } from 'shared/lib/locationParse'
import { useStore } from 'stores/useStore'
import { Tabs } from 'widgets/Tabs'
import { ItemsProps } from 'widgets/Tabs/Tabs'

import cls from './CalcModelPage.module.scss'
import { Generalcalculationparameters } from './ui/Generalcalculationparameters'
import { ListOfConsumptionSchedules } from './ui/ListOfConsumptionSchedules'
import { ListOfStations } from './ui/ListOfStations'

export interface CalcModelPageProps {
  className?: string
}

const CalcModelPage = observer((props: CalcModelPageProps) => {
  const { className } = props
  const { authStore } = useStore()
  const { userDetail } = authStore

  const history = useNavigate()

  const tabs: ItemsProps[] = [
    { key: 'listofstations', label: 'Перечень станций', icon: 'book', isView: true },
    {
      key: 'listofconsumptionschedules',
      label: 'Перечень графиков потребления',
      icon: 'book',
      isView: true,
    },
    {
      key: 'generalcalculationparameters',
      label: 'Общие параметры расчёта',
      icon: 'book',
      isView: true,
    },
  ]

  const { path = 'listofstations' } = locationParse(location.search)

  return (
    <div className={classNames(cls.CalcModelPage, {}, className ? [className] : [])}>
      <div className={classNames(cls.Header, {}, [])}>
        <Tabs
          items={tabs}
          selectedValue={path}
          onChange={(value) => {
            history(`?path=${value}`)
          }}
        />
      </div>
      <div className={classNames(cls.Body, {}, [])}>
        {path === 'listofstations' && <ListOfStations />}
        {path === 'listofconsumptionschedules' && <ListOfConsumptionSchedules userDetail={userDetail} />}
        {path === 'generalcalculationparameters' && <Generalcalculationparameters userDetail={userDetail} />}
      </div>
    </div>
  )
})

export default CalcModelPage
