import { observer } from 'mobx-react'
import { type FC, useState } from 'react'
import { Button } from 'shared/ui/Button'

import { IRguLimitConfirmResult } from './lib'
import { RguLimitModal, RguLimitModalProps } from './ui'

interface RguLimitProps {
  type: RguLimitModalProps['type']
  item: RguLimitModalProps['item']
  onChange?: (parameterName: string, value: IRguLimitConfirmResult[], mode: string) => void
}

export const RguLimit: FC<RguLimitProps> = observer((props) => {
  const { item, type, onChange } = props
  const [showModal, setShowModal] = useState<boolean>(false)

  const handleConfirm = (result: IRguLimitConfirmResult[]) => {
    onChange?.(type, result, '')
    setShowModal(false)
  }

  return (
    <>
      {item.parameterValue.value.turnedOn && (
        <Button variant='text' onClick={() => setShowModal(true)}>
          Список ограничений
        </Button>
      )}
      {showModal && (
        <RguLimitModal type={type} item={item} onClose={() => setShowModal(false)} onConfirm={handleConfirm} />
      )}
    </>
  )
})
