import { IconButton } from '@mui/material'
import { RguLimitRow } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/RightContainer/ui/Station/ui/ExtraSettings/ui/RguLimit/lib/useRguLimitTable.ts'
import { Icon } from 'shared/ui'
import { type IColumn } from 'widgets/TableV1'

import cls from '../ui/RguLimitModal/RguLimitModal.module.scss'
import { numberOption } from './numberOption'

const baseColumns: IColumn<RguLimitRow>[] = [
  {
    name: 'rguItem',
    title: 'РГЕ',
    width: 190,
    render: (_, row) => row.rguItem.label,
  },
  {
    name: 'restrictionItem',
    title: 'Причина',
    width: 222,
    render: (_, row) => row.restrictionItem.label,
  },
  {
    name: 'limit',
    title: 'Значение , МВт',
    width: 190,
    numberOption,
  },
]

type RguLimitModalColumns = (
  editModeRole: boolean,
  onDelete: (tabId: RguLimitRow['tabId']) => void,
) => IColumn<RguLimitRow>[]

export const rguLimitModalColumns: RguLimitModalColumns = (editModeRole, onDelete): IColumn<RguLimitRow>[] => {
  const columns: IColumn<RguLimitRow>[] = [...baseColumns]

  if (editModeRole) {
    columns.push({
      name: 'actions',
      title: ' ',
      width: 40,
      render: (_, row) => (
        <div className={cls.trashButtonContainer}>
          <IconButton onClick={() => onDelete(row.tabId)} className={cls.trashButton}>
            <Icon height={12} name='trash' />
          </IconButton>
        </div>
      ),
    })
  }

  return columns
}
