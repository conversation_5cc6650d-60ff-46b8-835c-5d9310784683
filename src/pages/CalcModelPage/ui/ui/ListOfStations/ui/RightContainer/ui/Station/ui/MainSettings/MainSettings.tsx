import { observer } from 'mobx-react'
import { HistoryParamsModalProps } from 'pages/CalcModelPage/ui/ui/HistoryParamsModal/ui/HistoryParamsModal.tsx'
import cls from 'pages/CalcModelPage/ui/ui/ListOfStations/ListOfStations.module.scss'
import { IPropsRight } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/RightContainer'
import { Dispatch, FC, SetStateAction, useEffect, useMemo, useRef, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { Icon } from 'shared/ui'
import { Button } from 'shared/ui/Button'
import { Select } from 'shared/ui/Select'
import { Switch } from 'shared/ui/Switch'
import { TextField } from 'shared/ui/TextField'
import { IPrepareRgu } from 'stores/CalcModelStore'
import { useStore } from 'stores/useStore.ts'
import { CustomTableCell, Table } from 'widgets/Table'

import { voltages } from '../../lib'

interface IRow extends Omit<Omit<IPrepareRgu, 'children'>, 'generators'> {
  name: string
  generators: string[]
  tabId: string
  isEdit: boolean
  id: number
  children: IRow[]
  avrchm: boolean
  voltage: string
}

interface MainSettingsProps {
  isEditPlant?: string | boolean
  editModeRole: IPropsRight['editModeRole']
  rows: IPropsRight['rows']
  setRows: IPropsRight['setRows']
  setIsHistoryModal: Dispatch<SetStateAction<HistoryParamsModalProps['object'] | null>>
  prepareData: (arr: IPrepareRgu[], parentId: number) => IPrepareRgu[]
  initialRows: IPrepareRgu[]
  adjustableUnit: string
  setAdjustableUnit: Dispatch<SetStateAction<string>>
  setEditMode: IPropsRight['setEditMode']
  optimization: string | boolean
  setOptimization: Dispatch<SetStateAction<string | boolean>>
  isEFFICIENCY: boolean
  efficiency: string
  setEfficiency: Dispatch<SetStateAction<string>>
  isLoadingTable: boolean
  onReset: () => void
}

export const MainSettings: FC<MainSettingsProps> = observer((props) => {
  const {
    isEditPlant,
    editModeRole,
    rows,
    setRows,
    setIsHistoryModal,
    prepareData,
    initialRows,
    adjustableUnit,
    setAdjustableUnit,
    setEditMode,
    optimization,
    setOptimization,
    isEFFICIENCY,
    efficiency,
    setEfficiency,
    isLoadingTable,
    onReset,
  } = props
  const { calcModelStore } = useStore()
  const { params, configs, typeStation, isPastDate, listOfStationsStore } = calcModelStore
  const { selectedPlant } = listOfStationsStore
  const [expandedRowIds, setExpandedRowIds] = useState<(string | null)[]>([])
  const [hiddenColumns, setHiddenColumns] = useState<string[]>([])

  const bodyRef = useRef<HTMLDivElement | null>(null)
  const [height, setHeight] = useState<number | null>(null)

  const changeHeightTable = () => {
    if (bodyRef.current) {
      const padding = getComputedStyle(bodyRef.current, null).getPropertyValue('padding')
      const curHeight = bodyRef.current.getBoundingClientRect().height - 2 * parseInt(padding)
      setHeight(curHeight)
    }
  }

  const setRef = (ref: HTMLDivElement | null) => {
    bodyRef.current = ref
    changeHeightTable()
  }

  const isGes = useMemo(() => {
    if (!selectedPlant) return false

    return /ГЭС/.test(selectedPlant.label)
  }, [selectedPlant])

  const adjustableUnitList = [
    { value: 'PLANT', label: 'Электростанция' },
    { value: 'RGU', label: 'РГЕ' },
    { value: 'GENERATOR', label: 'Гидрогенератор', hidden: true },
  ]

  const optimizationList = [
    { value: 'true', label: 'Оптимизируемая' },
    { value: 'false', label: 'Неоптимизируемая' },
  ]

  const columns = [
    {
      name: 'name',
      title: 'Конфигурация',
      width: 300,
      render: (value: string, row: IRow) => {
        const isGenerators = !!row.generators

        return (
          <div className={cls.NameConfig}>
            <div className={cls.IconConfig}>
              {isGenerators ? <Icon width={16} name='rge' /> : <Icon width={16} name='generator' />}
            </div>
            {value}
          </div>
        )
      },
    },
    {
      name: 'voltage',
      title: 'Напряжение, кВ',
      width: 300,
      editingEnabled: (isEditPlant && editModeRole) || !isPastDate,
      editType: 'select',
      chooseItems: voltages,
    },
    {
      name: 'startDate',
      title: 'Дата начала',
      width: 175,
      editingEnabled: false,
      editType: 'date',
    },
    {
      name: 'endDate',
      title: 'Дата окончания',
      width: 175,
      editingEnabled: false,
      editType: 'date',
    },
    {
      name: 'avrchm',
      title: 'Участие в АВРЧМ',
      width: 200,
      headRender: () => {
        return <div className={cls.ActionHeader}>Участие в АВРЧМ</div>
      },
      render: (value: boolean, row: IRow) => {
        const handleChangeSwitch = (_: unknown, checked: boolean) => {
          const editValue = (arr: IPrepareRgu[], parent?: boolean): IPrepareRgu[] => {
            return arr.map((item) =>
              item.tabId === row.tabId
                ? {
                    ...item,
                    isEdit: parent !== null ? parent : false,
                    avrchm: parent !== null && !parent ? false : checked,
                    ...(item.children?.length && {
                      children: item.children.map((i) => ({
                        ...i,
                        isEdit: true,
                        avrchm: checked,
                      })),
                    }),
                  }
                : item.children?.length
                  ? {
                      ...item,
                      children: editValue(item.children, item.avrchm),
                    }
                  : item,
            )
          }

          const newRows = editValue(rows).map((item) => ({
            ...item,
            avrchm: item?.children ? item?.children?.some((i) => i.avrchm) : false,
          }))
          setRows(newRows)
        }

        return (
          <div className={cls.ActionCell}>
            <Button
              onClick={() => {
                const isRgu = row?.generators && row?.generators?.length > 0
                setIsHistoryModal({
                  type: isRgu ? 'rgu' : 'generator',
                  id: row.id,
                  plantId: row.id,
                  generatorName: isRgu ? '' : row.name,
                  rguName: isRgu ? row.name : '',
                  parameterName: 'PARTICIPATION_AVRCHM',
                  name: 'Участие в АВРЧМ',
                })
              }}
              variant='text'
              className={classNames(
                cls.ButtonInfo,
                {
                  [cls.HiddenButton]: row?.generators && row?.generators?.length > 0,
                },
                [cls.ButtonHistory],
              )}
            >
              <Icon width={14} name='history' />
            </Button>
            <CustomTableCell>
              <Switch
                checked={value}
                onChange={(_: unknown, checked: boolean) => {
                  handleChangeSwitch(_, checked)
                }}
                disabled={!(isEditPlant && editModeRole) || isPastDate}
              />
            </CustomTableCell>
          </div>
        )
      },
    },
    {
      name: 'priorityLoad',
      title: 'Приоритетная загрузка',
      width: 200,
      headRender: () => {
        return <div className={cls.ActionHeader}>Приоритетная загрузка</div>
      },
      render: (value: boolean, row: IRow) => {
        const isGenerators = !!row.generators
        const handleChangeSwitch = (_: unknown, checked: boolean) => {
          const editValue = (arr: IPrepareRgu[]) => {
            return arr.map((item) =>
              item.tabId === row.tabId
                ? {
                    ...item,
                    isEdit: prepareData(configs, 0)?.find((i) => i.id === row.id)?.priorityLoad !== checked,
                    priorityLoad: checked,
                  }
                : item,
            )
          }
          const newRows = editValue(rows)
          setRows(newRows)
        }

        return (
          isGenerators && (
            <div className={cls.ActionCell}>
              <Button
                onClick={() => {
                  const isRgu = row?.generators && row?.generators?.length > 0
                  setIsHistoryModal({
                    type: isRgu ? 'rgu' : 'generator',
                    id: row.id,
                    plantId: row.id,
                    parameterName: 'PRIORITY_LOAD',
                    name: 'Приоритетная загрузка',
                    generatorName: isRgu ? '' : row.name,
                    rguName: isRgu ? row.name : undefined,
                  })
                }}
                variant='text'
                className={classNames(cls.ButtonInfo, {}, [cls.ButtonHistory])}
              >
                <Icon width={14} name='history' />
              </Button>
              <CustomTableCell>
                <Switch
                  checked={value}
                  onChange={handleChangeSwitch}
                  disabled={!(isEditPlant && editModeRole) || isPastDate}
                />
              </CustomTableCell>
            </div>
          )
        )
      },
    },
  ]

  useEffect(() => {
    window.addEventListener('resize', changeHeightTable)

    return () => {
      window.removeEventListener('resize', changeHeightTable)
      onReset()
    }
  }, [onReset])

  // При первоначальной загрузке развернуть все строки
  useEffect(() => {
    if (initialRows.length > 0) {
      const allTabIds = initialRows.map((el) => el?.tabId ?? null)
      setExpandedRowIds(allTabIds)
    }
  }, [initialRows])

  useEffect(() => {
    if (typeStation === 'GES') {
      setHiddenColumns([])
    } else {
      setHiddenColumns(['avrchm', 'priorityLoad'])
    }
  }, [typeStation])

  return (
    <>
      <div>
        {isGes && (
          <div className={cls.Row}>
            <div className={cls.Cell}>Регулируемая единица :</div>
            <div className={cls.Cell}>
              <Button
                variant='text'
                className={cls.ButtonHistoryForRegular}
                onClick={() => {
                  const item = params.find((el) => el.parameterName === 'REGULATED_UNIT')
                  item && setIsHistoryModal(item as unknown as HistoryParamsModalProps['object'])
                }}
              >
                <Icon width={14} name='history' />
              </Button>
              <Select
                variant='outlined'
                disabled={!isEditPlant || !editModeRole || isPastDate}
                value={adjustableUnit}
                items={adjustableUnitList}
                onChange={(value) => {
                  setAdjustableUnit(value)
                  setEditMode(true)
                }}
              />
            </div>
          </div>
        )}
        {isGes && (
          <div className={cls.Row}>
            <div className={cls.Cell}>Оптимизация :</div>
            <div className={cls.Cell}>
              <Button
                variant='text'
                className={cls.ButtonHistoryForRegular}
                onClick={() => {
                  const item = params.find((el) => el.parameterName === 'OPTIMIZATION')
                  item && setIsHistoryModal(item as unknown as HistoryParamsModalProps['object'])
                }}
              >
                <Icon width={14} name='history' />
              </Button>
              <Select
                variant='outlined'
                disabled={!isEditPlant || !editModeRole || isPastDate}
                value={optimization as unknown as string}
                items={optimizationList}
                onChange={(value) => {
                  setOptimization(value)
                  setEditMode(true)
                }}
              />
            </div>
          </div>
        )}
        {isEFFICIENCY && (
          <div className={cls.Row}>
            <div className={cls.Cell}>КПД ГАЭС :</div>
            <div className={cls.Cell}>
              <Button
                variant='text'
                className={cls.ButtonHistoryForRegular}
                onClick={() => {
                  const item = params.find((el) => el.parameterName === 'EFFICIENCY')
                  item && setIsHistoryModal(item as unknown as HistoryParamsModalProps['object'])
                }}
              >
                <Icon width={14} name='history' />
              </Button>
              <TextField
                value={efficiency}
                disabled={!isEditPlant || !editModeRole || isPastDate}
                className={cls.Efficiency}
                variant='standard'
                onChange={(e) => {
                  const value = e.target.value
                  if (Number(value) >= 1 && Number(value) <= 2 && String(e.target.value).length <= 5) {
                    setEfficiency(value)
                    setEditMode(true)
                  }
                }}
              />
            </div>
          </div>
        )}
      </div>
      <div ref={setRef} className={cls.Table}>
        <Table
          loading={isLoadingTable}
          childKey='name'
          height={height !== null ? height - 10 : 680}
          initialData={initialRows}
          rows={rows}
          columns={columns}
          setRows={setRows}
          editMode={(isEditPlant && editModeRole) || isPastDate}
          expandedRowIds={expandedRowIds}
          setExpandenRowIds={setExpandedRowIds}
          hiddenColumnNames={hiddenColumns}
          columnSearchDisabled={['avrchm', 'priorityLoad']}
          ROW_HEIGHT={27} // Фиксируем высоту строк, что бы они не "прыгали" при изменении состояния disabled
        />
      </div>
    </>
  )
})
