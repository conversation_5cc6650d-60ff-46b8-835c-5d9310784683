import { ICalcModelAvrchmSettings } from 'stores/CalcModelStore/ListOfStationsStore/ListOfStationsStore.types'

export interface IFormulaListItem {
  label: string
  value: string
}

export const kFormulaList = (columns: ICalcModelAvrchmSettings['columns']): IFormulaListItem[] => {
  return columns.map((column, idx) => ({
    value: `k${idx + 1}*[${idx + 1}]`,
    label: `k${idx + 1}*[${idx + 1}] - ${column.title}`,
  }))
}

export const bracketsFormulaList = (columns: ICalcModelAvrchmSettings['columns']): IFormulaListItem[] => {
  return columns.map((column, idx) => ({
    value: `[${idx + 1}]`,
    label: `[${idx + 1}] - ${column.title}`,
  }))
}
