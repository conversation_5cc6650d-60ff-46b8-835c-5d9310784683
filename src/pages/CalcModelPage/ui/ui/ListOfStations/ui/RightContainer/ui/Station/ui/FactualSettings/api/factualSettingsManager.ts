import { axiosInstance as api } from 'shared/lib/axios'

interface PlantDto {
  plantId: number
  name: string
}

interface TitledEnumDto {
  code: string
  title: string
}

interface PlantFactualSettingColumnDto {
  title: string
  parameter: TitledEnumDto
  accuracy?: number
  dayShift?: number
  defaultShow: boolean
  totalFormula?: TitledEnumDto
  comment?: string
}

export interface PlantFactualSettingDto {
  plant?: PlantDto
  showFactual: boolean
  columns: PlantFactualSettingColumnDto[]
}

export const getFactualSettings = (plantId: number): Promise<PlantFactualSettingDto> =>
  api.get(`/api/v1/plant/${plantId}/factual-settings`)

export const saveFactualSettings = (plantId: number, data: PlantFactualSettingDto): Promise<PlantFactualSettingDto> =>
  api.put(`/api/v1/plant/${plantId}/factual-settings`, data)

export const getFactualParameters = (plantId: number): Promise<TitledEnumDto[]> =>
  api.get(`/api/v1/plant/${plantId}/factual-parameters`)

export const getTotalFormulas = (): Promise<TitledEnumDto[]> => api.get('/api/v1/plant/total-formulas')
