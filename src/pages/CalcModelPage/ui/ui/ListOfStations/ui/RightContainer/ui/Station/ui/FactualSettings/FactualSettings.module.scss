.container {
  height: 100%;
  max-width: 1090px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  &Loader {
    width: 100%;
    max-width: 100%;
    justify-content: center;
    align-items: center;
  }

  &NoData {
    width: 100%;
    max-width: 100%;
    justify-content: center;
    align-items: center;
    color: var(--text-color);
  }
}

.switchShowFactual {
  width: 190px;
}

.table {
  &Disabled {
    filter: grayscale(1);
  }

  &ColumnHeader {
    display: flex;
    flex-direction: column;

    span {
      line-height: 18px;
    }
  }

  &ActionHeader {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &PrimaryButton {
    color: var(--primary-color);
    stroke-width: 0.5;
  }

  &DeleteButton {
    color: var(--red-color);
  }
}
