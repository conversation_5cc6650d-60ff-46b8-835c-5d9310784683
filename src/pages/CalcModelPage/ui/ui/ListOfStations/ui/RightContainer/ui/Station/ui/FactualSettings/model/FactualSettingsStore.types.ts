import { ItemsProps } from 'shared/ui/Select/Select.tsx'
import { RootStore } from 'stores/RootStore'

import { PlantFactualSettingDto } from '../api'

export interface IFactualSettingsFormData {
  title: string
  parameterCode: string
  accuracy?: string
  dayShift?: string
  defaultShow: boolean
  totalFormulaCode?: string
  comment?: string
}

export interface IFactualSetting extends Omit<PlantFactualSettingDto, 'columns'> {
  columns: (PlantFactualSettingDto['columns'][0] & { tabId: number })[]
}

export interface IFactualSettingsStore {
  rootStore: RootStore
  factualSettings: IFactualSetting | null
  originalFactualSettings: IFactualSetting | null
  totalFormulas: ItemsProps[]
  factualParameters: ItemsProps[]
  isFactualSettingsLoading: boolean
  isLoading: boolean
  selectedFactualSettingColumn: IFactualSetting['columns'][0] | null
  openedModal: boolean
  formData: IFactualSettingsFormData
  formErrors: Record<keyof IFactualSettingsFormData, string[]>

  getFactualSettings: (plantId: number) => void
  changeShowFactual: (showFactual: boolean) => void
  updateFormField: <K extends keyof IFactualSettingsStore['formData']>(
    field: K,
    value: IFactualSettingsStore['formData'][K],
  ) => void
  updateFactualSettingsColumn: (tabId: number) => void
  deleteFactualSettingsColumn: (tabId: number) => void
}
