.actions {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.action {
  margin: 0 10px !important;
}

.row {
  display: flex;
  align-items: center;
  margin: 4px 0;
  min-height: 36px;
}

.textField {
  width: 100%;
}

.body {
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
}

.titleEdit {
  color: var(--text-color);
  font-family: var(--font-family-main);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  display: flex;
  align-items: center;
  margin-right: 36px;
  width: 120px;
}

.selected {
  width: 218px;
  height: 30px;

  & > div > div {
    padding: 4px 12px !important;
  }
}

.minNormContainer {
  display: flex;
  gap: 12px;
  align-items: center;
}

.formField {
  align-items: start;
  min-height: 48px;
  margin: 0;

  &Input {
    min-width: 218px;
  }

  &Label {
    margin-top: 3px;
  }
}
