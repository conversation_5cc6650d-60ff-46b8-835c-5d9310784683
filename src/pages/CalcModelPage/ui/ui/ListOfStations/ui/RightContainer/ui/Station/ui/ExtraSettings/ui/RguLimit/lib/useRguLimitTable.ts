import { IParametersConfigsOutput } from 'entities/api/calcModelPage.entities.ts'
import { IToggleableRguRestrictionLimitParameter } from 'entities/shared/common.entities.ts'
import { RguLimitModalProps } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/RightContainer/ui/Station/ui/ExtraSettings/ui/RguLimit/ui'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { ItemsProps } from 'shared/ui/Select/Select.tsx'
import { useStore } from 'stores/useStore.ts'
import { IBaseRowData } from 'widgets/TableV1'

export interface RguLimitRow extends IBaseRowData {
  active: boolean
  rguItem: ItemsProps
  restrictionItem: ItemsProps
  limit: string
}

export interface IRguLimitConfirmResult {
  active?: boolean
  rguId?: number
  code?: number
  limit?: string
}

export const useRguLimitTable = (
  type: RguLimitModalProps['type'],
  item: IParametersConfigsOutput<IToggleableRguRestrictionLimitParameter['value']['value']>,
) => {
  const { calcModelStore } = useStore()
  const { restriction, initLimits, configs } = calcModelStore

  const initialRows = useRef<RguLimitRow[]>([])
  const initialSelectedIds = useRef<(string | number)[]>([])
  const [rows, setRows] = useState<RguLimitRow[]>([])
  const [selectedIds, setSelectedIds] = useState<(string | number)[]>([])

  const [selectedRguId, setSelectedRguId] = useState<string | null>(null)
  const [selectedRestrictionCode, setSelectedRestrictionCode] = useState<string | null>(null)
  const [limit, setLimit] = useState<string>('0')

  const hasChanges = useMemo(() => {
    const hasEditedRows = JSON.stringify(initialRows.current) !== JSON.stringify(rows)
    const hasEditedSelectedIds = JSON.stringify(initialSelectedIds.current) !== JSON.stringify(selectedIds)

    return hasEditedRows || hasEditedSelectedIds
  }, [rows, selectedIds])

  const restrictionItems = useMemo(
    () =>
      restriction
        .filter((restrictionItem) => !rows.some((row) => row.restrictionItem.value === restrictionItem.code))
        .map((restrictionItem) => ({
          label: restrictionItem.name,
          value: restrictionItem.code,
        })),
    [restriction, rows],
  )

  const rguItems = useMemo(
    () =>
      configs.map((config) => ({
        value: config.id,
        label: config.name,
      })),
    [configs],
  )

  useEffect(() => {
    initLimits(type === 'RGU_MINIMUM_LIMIT' ? 'min' : 'max', false)
  }, [])

  useEffect(() => {
    const initRows: RguLimitRow[] = []
    const initSelectedIds: (string | number)[] = []
    if (item.parameterValue.value.value?.restrictions.length) {
      item.parameterValue.value.value.restrictions.forEach((restriction, idx) => {
        const restrictionItem = restrictionItems.find((item) => item.value === Number(restriction.code))
        const rguItem = rguItems.find((item) => item.value === Number(restriction.rguId))

        if (restrictionItem && rguItem) {
          if (idx === 0) {
            setSelectedRguId(String(rguItem.value))
            setSelectedRestrictionCode(String(restrictionItem.value))
          }

          const tabId = Date.now()
          initRows.push({
            tabId,
            restrictionItem,
            rguItem,
            active: restriction.active,
            limit: String(restriction.limit),
            disabledChecked: false,
            isEdit: false,
            children: [],
          })
          initSelectedIds.push(tabId)
        }
      })
    }
    setSelectedIds(initSelectedIds)
    setRows(initRows)
    initialSelectedIds.current = initSelectedIds
    initialRows.current = initRows
  }, [item])

  useEffect(() => {
    if (restrictionItems.length) {
      setSelectedRestrictionCode(String(restrictionItems[0].value))
    }
    if (rguItems.length) {
      setSelectedRguId(String(rguItems[0].value))
    }
  }, [restrictionItems, rguItems])

  const handleReset = useCallback(() => {
    setRows(initialRows.current)
    setSelectedIds(initialSelectedIds.current)
  }, [])

  const handleAddRestriction = useCallback(() => {
    const restrictionItem = restrictionItems.find((item) => item.value === Number(selectedRestrictionCode))
    const rguItem = rguItems.find((item) => item.value === Number(selectedRguId))

    if (!restrictionItem || !rguItem) return

    const tabId = Date.now()
    const newRow: RguLimitRow = {
      tabId,
      active: true,
      restrictionItem,
      rguItem,
      limit: String(Number(limit)),
      // Нужно для отображения чекбоксов
      disabledChecked: false,
      isEdit: true,
    }
    setSelectedIds((prevIds) => [...prevIds, tabId])
    setRows((prev) => [...prev, newRow])
  }, [restriction, selectedRestrictionCode, limit])

  const handleDeleteRestriction = useCallback((tabId: number) => {
    setRows((prev) => prev.filter((row) => row.tabId !== tabId))
    setSelectedIds((prev) => prev.filter((id) => id !== String(tabId)))
  }, [])

  const handleSave = useCallback(
    (onConfirm?: (res: IRguLimitConfirmResult[]) => void) => {
      const result: IRguLimitConfirmResult[] = rows.map((row) => ({
        active: selectedIds.includes(String(row.tabId)),
        code: Number(row.restrictionItem.value),
        limit: row.limit,
        rguId: Number(row.rguItem.value),
      }))
      onConfirm?.(result)
    },
    [rows, selectedIds, restrictionItems, rguItems],
  )

  const canAddRestriction = selectedRestrictionCode !== null && limit.length > 0

  return {
    rows,
    setRows,
    selectedIds,
    setSelectedIds,
    selectedRguId,
    setSelectedRguId,
    selectedRestrictionCode,
    setSelectedRestrictionCode,
    limit,
    setLimit,
    hasChanges,
    restrictionItems,
    rguItems,
    canAddRestriction,
    handleReset,
    handleAddRestriction,
    handleDeleteRestriction,
    handleSave,
  }
}
