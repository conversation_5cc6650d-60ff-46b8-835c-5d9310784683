import { ROLES } from 'entities/shared/roles.entities.ts'
import { makeAutoObservable, runInAction } from 'mobx'
import { IDisplaySettingsStore } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/RightContainer/ui/Station/ui/DisplaySettings/model/DisplaySettingsStore.types.ts'
import { validateFactualSettingsForm } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/RightContainer/ui/Station/ui/FactualSettings/lib/validateFactualSettingsForm.ts'
import { klona } from 'shared/lib/klona'
import { RootStore } from 'stores/RootStore'

import { getFactualParameters, getFactualSettings, getTotalFormulas, saveFactualSettings } from '../api'
import { IFactualSetting, IFactualSettingsStore } from './FactualSettingsStore.types.ts'

export const factualSettingsColumnErrorObjectInit: IFactualSettingsStore['formErrors'] = {
  title: [],
  parameterCode: [],
  accuracy: [],
  dayShift: [],
  defaultShow: [],
  totalFormulaCode: [],
  comment: [],
}

const initFormData = {
  title: '',
  parameterCode: '',
  accuracy: '',
  dayShift: '',
  defaultShow: false,
  totalFormulaCode: '',
  comment: '',
}

export class FactualSettingsStore implements IFactualSettingsStore {
  rootStore: IFactualSettingsStore['rootStore']
  factualSettings: IFactualSettingsStore['factualSettings'] = null
  originalFactualSettings: IFactualSettingsStore['originalFactualSettings'] = null
  totalFormulas: IFactualSettingsStore['totalFormulas'] = []
  factualParameters: IFactualSettingsStore['factualParameters'] = []
  isFactualSettingsLoading: IFactualSettingsStore['isFactualSettingsLoading'] = true
  isLoading: IFactualSettingsStore['isLoading'] = false
  selectedFactualSettingColumn: IFactualSettingsStore['selectedFactualSettingColumn'] = null
  openedModal: IFactualSettingsStore['openedModal'] = false
  formData: IFactualSettingsStore['formData'] = structuredClone(initFormData)
  formErrors: IFactualSettingsStore['formErrors'] = structuredClone(factualSettingsColumnErrorObjectInit)

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this)
  }

  get isDisplaySettingsChanged(): IDisplaySettingsStore['isDisplaySettingsChanged'] {
    return JSON.stringify(this.factualSettings) !== JSON.stringify(this.originalFactualSettings)
  }

  get hasSomeFormErrors(): boolean {
    return Object.entries(this.formErrors).some(([, value]) => value.length > 0)
  }

  get allowEditForm(): boolean {
    const selectedPlant = this.rootStore.calcModelStore.listOfStationsStore.selectedPlant

    return (
      this.rootStore.authStore.userDetail.roles
        .map((el) => el.role)
        .some((el) => [ROLES.TECH_ADMIN_CM].some((item) => item === el)) &&
      !selectedPlant?.viewOnly &&
      !!selectedPlant?.active
    )
  }

  get allowEditTable(): boolean {
    return !!this.factualSettings?.showFactual
  }

  private readonly _resetFormData = () => {
    this.formData = structuredClone(initFormData)
    this.formErrors = structuredClone(factualSettingsColumnErrorObjectInit)
  }

  private readonly _populateFormData = (
    columnSettings: Exclude<IFactualSettingsStore['factualSettings'], null>['columns'][0],
  ) => {
    this.formData = {
      title: columnSettings.title,
      parameterCode: columnSettings.parameter.code,
      accuracy: String(columnSettings.accuracy),
      dayShift: String(columnSettings.dayShift),
      defaultShow: columnSettings.defaultShow,
      totalFormulaCode: columnSettings.totalFormula?.code,
      comment: columnSettings.comment,
    }
  }

  private readonly _convertFormDataToColumnSettings = (
    tabId: number,
    parameter: FactualSettingsStore['factualParameters'][0],
  ): IFactualSetting['columns'][0] => {
    const totalFormula = this.totalFormulas.find(
      (totalFormula) => totalFormula.value === this.formData.totalFormulaCode,
    )

    return {
      tabId: tabId,
      title: this.formData.title,
      parameter: {
        code: parameter.value as string,
        title: parameter.label as string,
      },
      accuracy: Number(this.formData.accuracy),
      dayShift: Number(this.formData.dayShift),
      defaultShow: this.formData.defaultShow,
      totalFormula: totalFormula
        ? {
            code: String(totalFormula.value),
            title: String(totalFormula.label),
          }
        : undefined,
      comment: this.formData.comment?.trim(),
    }
  }

  getFactualSettings: IFactualSettingsStore['getFactualSettings'] = async (plantId) => {
    try {
      this.isFactualSettingsLoading = true
      const factualSettingsResponse = await getFactualSettings(plantId)
      const factualSettings = {
        ...factualSettingsResponse,
        columns: factualSettingsResponse.columns.map((column, idx) => ({ ...column, tabId: idx })),
      }
      runInAction(() => {
        this.factualSettings = factualSettings
        this.originalFactualSettings = factualSettings
        this.isFactualSettingsLoading = false
      })
    } catch (e) {
      this.isFactualSettingsLoading = false
      console.error(e)
    }
  }

  getTotalFormulas = async () => {
    try {
      const totalFormulas = await getTotalFormulas()
      this.totalFormulas = totalFormulas.map((totalFormula) => ({
        value: totalFormula.code,
        label: totalFormula.title,
      }))
    } catch (e) {
      console.error(e)
    }
  }

  getFactualParameters = async () => {
    const selectedPlant = this.rootStore.calcModelStore.listOfStationsStore.selectedPlant
    if (!selectedPlant) return
    try {
      const factualParameters = await getFactualParameters(selectedPlant.plantId)
      this.factualParameters = factualParameters.map((factualParameter) => ({
        value: factualParameter.code,
        label: factualParameter.title,
      }))
    } catch (e) {
      console.error(e)
    }
  }

  changeShowFactual: IFactualSettingsStore['changeShowFactual'] = (showFactual) => {
    if (!this.factualSettings) return
    this.factualSettings = {
      ...klona(this.factualSettings),
      showFactual,
    }
  }

  updateFormField: IFactualSettingsStore['updateFormField'] = (field, value) => {
    runInAction(() => {
      this.formData = {
        ...this.formData,
        [field]: value,
      }
      this.formErrors = {
        ...this.formErrors,
        [field]: [],
      }
    })
  }

  createFactualSettingsColumn = () => {
    runInAction(() => {
      this.openedModal = true
      this._resetFormData()
    })
  }

  updateFactualSettingsColumn: IFactualSettingsStore['updateFactualSettingsColumn'] = (tabId) => {
    runInAction(() => {
      const columnSettings = this.factualSettings?.columns.find((column) => column.tabId === tabId)
      if (columnSettings) {
        this._populateFormData(columnSettings)
        this.selectedFactualSettingColumn = columnSettings
        this.openedModal = true
      }
    })
  }

  addFactualSettingsColumnToTable = () => {
    if (!this.factualSettings) return false

    this.formErrors = validateFactualSettingsForm(this.formData)
    const hasError = Object.entries(this.formErrors).some(([, value]) => value.length > 0)

    if (hasError) return false

    const parameter = this.factualParameters.find((parameter) => parameter.value === this.formData.parameterCode)
    if (!parameter) return false

    if (this.selectedFactualSettingColumn) {
      this.factualSettings = {
        ...klona(this.factualSettings),
        columns: this.factualSettings.columns.map((factualSetting) => {
          if (factualSetting.tabId === this.selectedFactualSettingColumn?.tabId) {
            return this._convertFormDataToColumnSettings(factualSetting.tabId, parameter)
          }

          return factualSetting
        }),
      }
    } else {
      this.factualSettings = {
        ...klona(this.factualSettings),
        columns: this.factualSettings.columns.concat(
          this._convertFormDataToColumnSettings(this.factualSettings.columns.length, parameter),
        ),
      }
    }

    return true
  }

  deleteFactualSettingsColumn: IFactualSettingsStore['deleteFactualSettingsColumn'] = (tabId) => {
    if (!this.factualSettings) return
    this.factualSettings = {
      ...klona(this.factualSettings),
      columns: this.factualSettings.columns.filter((column) => column.tabId !== tabId),
    }
  }

  saveFactualSettings = async () => {
    const selectedPlant = this.rootStore.calcModelStore.listOfStationsStore.selectedPlant
    if (!selectedPlant || !this.factualSettings) return

    this.isLoading = true
    try {
      const factualSettingsResponse = await saveFactualSettings(selectedPlant.plantId, this.factualSettings)
      const factualSettings = {
        ...factualSettingsResponse,
        columns: factualSettingsResponse.columns.map((column, idx) => ({ ...column, tabId: idx })),
      }
      this.factualSettings = factualSettings
      this.originalFactualSettings = factualSettings
      this.resetChanges()
      this.rootStore.notificationStore.addNotification({
        title: 'Сохранение РМ',
        type: 'success',
        description: 'Изменения сохранены',
      })
    } catch (error) {
      console.error(error)
    } finally {
      this.isLoading = false
    }
  }

  resetChanges = () => {
    this.factualSettings = klona(this.originalFactualSettings)
  }

  closeModal = () => {
    this.openedModal = false
    this.selectedFactualSettingColumn = null
    this.factualParameters = []
    this.totalFormulas = []
    this._resetFormData()
  }
}
