import { Icon<PERSON><PERSON>on, Tooltip } from '@mui/material'
import { Icon } from 'shared/ui'
import { IColumn } from 'widgets/TableV1'

import cls from '../FactualSettings.module.scss'
import { FactualSettingsStore } from '../model'

type FactualSettingsColumn = Exclude<FactualSettingsStore['factualSettings'], null>['columns'][0]

type FactualSettingsColumns = (
  onAdd: () => void,
  onEdit: (tabId: number) => void,
  onDelete: (tabId: number) => void,
  allowEditForm: boolean,
) => IColumn<FactualSettingsColumn>[]

export const factualSettingsColumns: FactualSettingsColumns = (onAdd, onEdit, onDelete, allowEditForm) => [
  {
    name: 'title',
    title: 'Наименование столбца',
    width: 200,
  },
  {
    name: 'parameter',
    title: 'Параметр',
    width: 100,
    render: (value: FactualSettingsColumn['parameter']) => value.title,
  },
  {
    name: 'accuracy',
    title: 'Точность',
    width: 90,
  },
  {
    name: 'dayShift',
    title: 'Сутки',
    width: 90,
  },
  {
    name: 'defaultShow',
    title: 'Отображение на графике по умолчанию',
    width: 200,
    editing: {
      type: 'switch',
      enabled: false,
    },
    headRender: () => (
      <div className={cls.tableColumnHeader}>
        <span>Отображение на</span>
        <span>графике по умолчанию</span>
      </div>
    ),
  },
  {
    name: 'comment',
    title: 'Комментарий',
    width: 300,
  },
  {
    name: 'action',
    title: '',
    width: 70,
    headRender: () => {
      return (
        <div className={cls.tableActionHeader}>
          <Tooltip title='Добавить параметр фактических данных'>
            <IconButton disabled={!allowEditForm} className={cls.tablePrimaryButton} onClick={onAdd}>
              <Icon name='plus' width={13} height={13} />
            </IconButton>
          </Tooltip>
        </div>
      )
    },
    render: (_, row) => (
      <div className={cls.actions}>
        <Tooltip title='Редактировать параметр фактических данных'>
          <IconButton disabled={!allowEditForm} className={cls.tablePrimaryButton} onClick={() => onEdit(row.tabId)}>
            <Icon name='settings' width={13} height={13} />
          </IconButton>
        </Tooltip>

        <Tooltip title='Удалить параметр фактических данных'>
          <IconButton disabled={!allowEditForm} className={cls.tableDeleteButton} onClick={() => onDelete(row.tabId)}>
            <Icon name='trash' width={13} height={13} />
          </IconButton>
        </Tooltip>
      </div>
    ),
  },
]
