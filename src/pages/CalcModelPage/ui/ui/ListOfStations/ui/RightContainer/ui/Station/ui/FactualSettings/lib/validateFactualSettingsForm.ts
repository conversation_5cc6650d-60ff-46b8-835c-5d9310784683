import { factualSettingsColumnErrorObjectInit, IFactualSettingsFormData, IFactualSettingsStore } from '../model'

export const validateFactualSettingsForm = (formData: IFactualSettingsFormData) => {
  const errors: IFactualSettingsStore['formErrors'] = structuredClone(factualSettingsColumnErrorObjectInit)
  // Валидация названия
  if (!formData.title) {
    errors['title'].push('Поле должно быть заполнено')
  }
  if (formData.title.length > 100) {
    errors['title'].push('Название должно содержать не более 100 символов')
  }

  // Валидация комментария
  if (formData.comment && formData.comment.length > 300) {
    errors['comment'].push(' Комментарий должен содержать не более 300 символов')
  }

  return errors
}
