.bodyModal {
  width: 700px;
  height: 400px;
  display: flex;
  flex-direction: column;
}

.tableModal {
  width: 100%;
  height: 100%;
}

.descriptionModal {
  width: 100%;
  height: 20px;
  font-style: normal;
  line-height: normal;
  color: rgb(150 150 150);
  font-size: 1.125rem;
  font-weight: 600;
}

.filterRow {
  width: 100%;
  height: 25px;
  display: flex;
  justify-content: space-around;
  margin: 10px 0;
}

.rguSelected {
  width: 180px;
  height: 14px;
}

.limitSelected {
  width: 280px;
  height: 14px;
}

.textField {
  width: 100px;
  margin: 0 8px;
  input {
    padding: 0 16px !important;
    height: 25px !important;
  }
}

.trashButtonContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  height: 24px;
}

.trashButton:hover {
  color: var(--red-color) !important;
}

.buttons {
  width: 100%;
  height: 43px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.button {
  margin: 0 10px !important;
}
