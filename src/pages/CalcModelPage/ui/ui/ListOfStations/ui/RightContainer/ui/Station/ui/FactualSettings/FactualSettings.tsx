import { observer } from 'mobx-react'
import { useEffect, useMemo } from 'react'
import { classNames } from 'shared/lib/classNames'
import { Loader, Switch } from 'shared/ui'
import { useStore } from 'stores/useStore.ts'
import { TableV1 } from 'widgets/TableV1'

import cls from './FactualSettings.module.scss'
import { factualSettingsColumns } from './lib'
import { ModalCreateEditFactualSetting } from './ui'

export const FactualSettings = observer(() => {
  const { calcModelStore } = useStore()
  const {
    listOfStationsStore: { selectedPlant, factualSettingsStore },
  } = calcModelStore
  const {
    allowEditForm,
    allowEditTable,
    getFactualSettings,
    factualSettings,
    isFactualSettingsLoading,
    openedModal,
    changeShowFactual,
    createFactualSettingsColumn,
    updateFactualSettingsColumn,
    deleteFactualSettingsColumn,
  } = factualSettingsStore

  useEffect(() => {
    if (selectedPlant?.plantId) {
      getFactualSettings(selectedPlant.plantId)
    }
  }, [selectedPlant])

  const columns = useMemo(
    () =>
      factualSettingsColumns(
        createFactualSettingsColumn,
        updateFactualSettingsColumn,
        deleteFactualSettingsColumn,
        allowEditTable && allowEditForm,
      ),
    [allowEditTable, allowEditForm],
  )

  if (isFactualSettingsLoading) {
    return (
      <div className={classNames(cls.container, {}, [cls.containerLoader])}>
        <Loader />
      </div>
    )
  }

  if (!factualSettings) {
    return <div className={classNames(cls.container, {}, [cls.containerNoData])}>Нет данных</div>
  }

  return (
    <div className={cls.container}>
      <div className={cls.switchShowFactual}>
        <Switch
          label='Фактические данные'
          checked={factualSettings.showFactual}
          disabled={!allowEditForm}
          onChange={(_, value) => changeShowFactual(value)}
        />
      </div>
      <TableV1
        rowHeight='auto'
        columns={columns}
        rows={factualSettings.columns}
        className={classNames('', {
          [cls.tableDisabled]: !allowEditTable,
        })}
      />
      {openedModal && <ModalCreateEditFactualSetting />}
    </div>
  )
})
