import { observer } from 'mobx-react'
import { useEffect, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { ErrorExplanationIcon, Loader, LoadingButton, Modal, Row, Select, Switch, TextField } from 'shared/ui'
import { useStore } from 'stores/useStore.ts'

import cls from './ModalCreateEditFactualSetting.module.scss'

export const ModalCreateEditFactualSetting = observer(() => {
  const {
    calcModelStore: {
      listOfStationsStore: { factualSettingsStore },
    },
  } = useStore()
  const {
    isLoading,
    hasSomeFormErrors,
    selectedFactualSettingColumn,
    formData,
    formErrors,
    totalFormulas,
    factualParameters,
    getTotalFormulas,
    getFactualParameters,
    updateFormField,
    addFactualSettingsColumnToTable,
    closeModal,
  } = factualSettingsStore
  const [loading, setLoading] = useState(true)
  const isCreationForm = selectedFactualSettingColumn === null

  useEffect(() => {
    Promise.all([getTotalFormulas(), getFactualParameters()]).then(() => {
      setLoading(false)
    })
  }, [])

  useEffect(() => {
    if (!loading) {
      if (factualParameters.length > 0 && !formData.parameterCode) {
        updateFormField('parameterCode', String(factualParameters[0].value))
      }
    }
  }, [loading])

  const handleSave = () => {
    const succeed = addFactualSettingsColumnToTable()
    if (succeed) {
      closeModal()
    }
  }

  return (
    <Modal
      open
      title={(isCreationForm ? 'Создание' : 'Редактирование') + ' фактического параметра'}
      maxWidth='lg'
      onClose={closeModal}
      actions={
        <div className={cls.modalFooter}>
          <div className={cls.modalFooterRight}>
            <LoadingButton
              variant='contained'
              onClick={handleSave}
              loading={isLoading}
              disabled={hasSomeFormErrors}
              className={classNames(cls.saveButton, { [cls.saveButtonEdit]: !isCreationForm })}
            >
              {isCreationForm ? 'Добавить' : 'Изменить'}
            </LoadingButton>
          </div>
        </div>
      }
    >
      <div className={classNames(cls.wrapper, { [cls.wrapperLoading]: loading }, [])}>
        {loading ? (
          <Loader />
        ) : (
          <>
            <Row label='Наименование столбца' contentClassName={cls.rowContent}>
              <TextField
                type='string'
                className={cls.rowContentField}
                value={formData.title}
                error={!!formErrors.title.length}
                onChange={(e) => updateFormField('title', e.target.value)}
              />
              <ErrorExplanationIcon title={formErrors.title.join('; ')} />
            </Row>
            <Row label='Параметр' contentClassName={cls.rowContent}>
              <Select
                variant='outlined'
                className={cls.rowContentField}
                items={factualParameters}
                value={formData.parameterCode}
                onChange={(value) => updateFormField('parameterCode', String(value))}
              />
            </Row>
            <Row label='Точность' contentClassName={cls.rowContent}>
              <TextField
                type='number'
                numberOption={{
                  positive: true,
                  isInteger: true,
                  max: 9,
                }}
                className={cls.rowContentField}
                value={formData.accuracy ?? ''}
                onChange={(e) => updateFormField('accuracy', e.target.value)}
              />
            </Row>
            <Row label='Сутки' contentClassName={cls.rowContent}>
              <TextField
                type='number'
                numberOption={{
                  isInteger: true,
                  min: -999,
                  max: 999,
                }}
                className={cls.rowContentField}
                value={formData.dayShift ?? ''}
                onChange={(e) => updateFormField('dayShift', e.target.value)}
              />
            </Row>
            <Row label='Отображение на графике по умолчанию'>
              <Switch checked={formData.defaultShow} onChange={(_, value) => updateFormField('defaultShow', value)} />
            </Row>
            <Row label='Итоговая строка' contentClassName={cls.rowContent}>
              <Select
                variant='outlined'
                addEmptyItem
                items={totalFormulas}
                value={formData.totalFormulaCode}
                className={cls.rowContentField}
                onChange={(value) => updateFormField('totalFormulaCode', String(value))}
              />
            </Row>
            <Row label='Комментарий' rowClassName={cls.row} contentClassName={cls.rowContent}>
              <TextField
                multiline
                maxRows={3}
                type='string'
                className={cls.rowContentField}
                value={formData.comment}
                error={!!formErrors.comment.length}
                onChange={(e) => updateFormField('comment', e.target.value)}
              />
              <ErrorExplanationIcon title={formErrors.comment.join('; ')} />
            </Row>
          </>
        )}
      </div>
    </Modal>
  )
})
