import { ChangeEvent, Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react'
import { Button } from 'shared/ui/Button/index.ts'
import { FormField } from 'shared/ui/FormField/index.ts'
import { Modal } from 'shared/ui/Modal/index.ts'
import { Select } from 'shared/ui/Select/index.ts'
import { TextField } from 'shared/ui/TextField/index.ts'
import { TextFieldProps } from 'shared/ui/TextField/model/types.ts'
import { CalcModelAvrchmSettingsSource } from 'stores/CalcModelStore/ListOfStationsStore/ListOfStationsStore.types.ts'
import { useStore } from 'stores/useStore.ts'

import cls from './AVRCHMSettingsModal.module.scss'
import { AvrchmSettingsFormErrors, convertPlantsToSelectFormat, sources, validate } from './lib'
import { FormulaField } from './ui/FormulaField/FormulaField.tsx'

export interface ModalSettingsProps {
  onClose: () => void
}

type HandleChangeNumber<T> = (
  e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  setState: Dispatch<SetStateAction<T>>,
) => void

export const AVRCHMSettingsModal = (props: ModalSettingsProps) => {
  const { onClose } = props
  const { calcModelStore } = useStore()
  const { plants, listOfStationsStore } = calcModelStore
  const { addAvrchmSettingsColumn, editableAvrchmSetting, editAvrchmSettingsColumn } = listOfStationsStore
  const [title, setTitle] = useState<string>('')
  const [source, setSource] = useState<CalcModelAvrchmSettingsSource>(sources[0].value)
  const [marketCalcModelId, setMarketCalcModelId] = useState<string>()
  const [influence, setInfluence] = useState<string>()
  const [minNorm, setMinNorm] = useState<string>()
  const [formula, setFormula] = useState<string>()
  const [errors, setErrors] = useState<AvrchmSettingsFormErrors>({})

  const plantsForSelect = useMemo(() => convertPlantsToSelectFormat(plants), [plants])
  const [plantId, setPlantId] = useState<string>()

  const modalTitle = useMemo(() => {
    if (editableAvrchmSetting) {
      return 'Редактирование столбца'
    }

    return 'Создание столбца'
  }, [editableAvrchmSetting])

  const actionTitle = useMemo(() => {
    if (editableAvrchmSetting) {
      return 'Сохранить'
    }

    return 'Добавить'
  }, [editableAvrchmSetting])

  const handleChangeTitle: TextFieldProps['onChange'] = (e) => setTitle(e.target.value)

  const handleChangeNumber: HandleChangeNumber<string | undefined> = (e, setState) => {
    const value = e.target.value
    if (value === '') {
      setState(undefined)
    } else {
      setState(value)
    }
  }

  const handleClose = () => {
    onClose()
    editAvrchmSettingsColumn(0, 'end')
  }

  const handleSaveColumn = () => {
    const plant = plants.find((plant) => plant.plantId === Number(plantId))
    const avrchmColumnSettings = {
      title,
      source,
      plant: plant
        ? {
            plantId: plant.plantId,
            name: plant.name,
          }
        : plant,
      marketCalcModelId: marketCalcModelId ? Number(marketCalcModelId) : undefined,
      formula,
      influence: influence ? Number(influence) : undefined,
      minNorm: minNorm ? Number(minNorm) : undefined,
    }
    const errors = validate(avrchmColumnSettings)
    setErrors(errors)

    if (Object.keys(errors).length > 0) return

    if (!editableAvrchmSetting) {
      addAvrchmSettingsColumn(avrchmColumnSettings)
    } else {
      editAvrchmSettingsColumn(editableAvrchmSetting.idx, 'apply', avrchmColumnSettings)
    }
    onClose()
  }

  useEffect(() => {
    if (editableAvrchmSetting) {
      setTitle(editableAvrchmSetting.avrchmSetting.title)
      setSource(editableAvrchmSetting.avrchmSetting.source)
      setPlantId(
        editableAvrchmSetting.avrchmSetting.plant?.plantId
          ? String(editableAvrchmSetting.avrchmSetting.plant?.plantId)
          : undefined,
      )
      setMarketCalcModelId(
        editableAvrchmSetting.avrchmSetting.marketCalcModelId !== undefined
          ? String(editableAvrchmSetting.avrchmSetting.marketCalcModelId)
          : undefined,
      )
      setInfluence(
        editableAvrchmSetting.avrchmSetting.influence !== undefined
          ? String(editableAvrchmSetting.avrchmSetting.influence)
          : undefined,
      )
      setMinNorm(
        editableAvrchmSetting.avrchmSetting.minNorm !== undefined
          ? String(editableAvrchmSetting.avrchmSetting.minNorm)
          : undefined,
      )
      setFormula(editableAvrchmSetting.avrchmSetting.formula)
    }
  }, [editableAvrchmSetting])

  return (
    <Modal
      fullWidth
      maxWidth='sm'
      onClose={handleClose}
      title={modalTitle}
      actions={[
        <div key='add' className={cls.actions}>
          <Button className={cls.action} onClick={handleSaveColumn}>
            {actionTitle}
          </Button>
        </div>,
      ]}
    >
      <div className={cls.body}>
        <FormField
          className={cls.formField}
          labelClassName={cls.formFieldLabel}
          fieldClassName={cls.formFieldInput}
          label='Наименование'
          field={
            <TextField
              value={title}
              onChange={handleChangeTitle}
              className={cls.textField}
              maxLength={255}
              error={!!errors?.title}
              helperText={errors?.title}
            />
          }
        />
        <FormField
          className={cls.formField}
          labelClassName={cls.formFieldLabel}
          fieldClassName={cls.formFieldInput}
          label='Источник данных'
          field={
            <Select<CalcModelAvrchmSettingsSource>
              variant='outlined'
              className={cls.selected}
              items={sources}
              value={source}
              onChange={setSource}
            />
          }
        />
        {source === CalcModelAvrchmSettingsSource.NEPTUNE && (
          <FormField
            className={cls.formField}
            fieldClassName={cls.formFieldInput}
            labelClassName={cls.formFieldLabel}
            label='Станция'
            field={
              <Select
                variant='outlined'
                className={cls.selected}
                items={plantsForSelect}
                value={plantId}
                onChange={setPlantId}
                error={!!errors?.plant}
                helperText={errors?.plant}
              />
            }
          />
        )}
        {source === CalcModelAvrchmSettingsSource.MODES && (
          <FormField
            className={cls.formField}
            labelClassName={cls.formFieldLabel}
            fieldClassName={cls.formFieldInput}
            label='ID'
            field={
              <TextField
                type='number'
                numberOption={{
                  positive: true,
                  isInteger: true,
                }}
                value={isNaN(Number(marketCalcModelId)) ? '' : String(marketCalcModelId)}
                onChange={(e) => handleChangeNumber(e, setMarketCalcModelId)}
                className={cls.textField}
                error={!!errors?.marketCalcModelId}
                helperText={errors?.marketCalcModelId}
              />
            }
          />
        )}
        <FormField
          className={cls.formField}
          labelClassName={cls.formFieldLabel}
          fieldClassName={cls.formFieldInput}
          label='Коэффициент влияния'
          field={
            <TextField
              type='number'
              toFixed={6}
              positiveNumber
              value={isNaN(Number(influence)) ? '' : String(influence)}
              onChange={(e) => handleChangeNumber(e, setInfluence)}
              className={cls.textField}
            />
          }
        />
        <FormField
          className={cls.formField}
          labelClassName={cls.formFieldLabel}
          fieldClassName={cls.formFieldInput}
          label='Норматив'
          field={
            <TextField
              type='number'
              toFixed={3}
              positiveNumber
              value={isNaN(Number(minNorm)) ? '' : String(minNorm)}
              onChange={(e) => handleChangeNumber(e, setMinNorm)}
              className={cls.textField}
            />
          }
        />
        {source === CalcModelAvrchmSettingsSource.FORMULA && (
          <FormulaField
            className={cls.formField}
            labelClassName={cls.formFieldLabel}
            fieldClassName={cls.formFieldInput}
            onChange={setFormula}
            error={!!errors?.formula}
            helperText={errors?.formula}
            value={formula}
          />
        )}
      </div>
    </Modal>
  )
}
