import { IParametersConfigsOutput, ValueConfigsOutput } from 'entities/api/calcModelPage.entities.ts'
import { IToggleableRestrictionCorridorParameter } from 'entities/shared/common.entities.ts'
import { Dispatch, FC, SetStateAction } from 'react'
import { Select, TextField } from 'shared/ui'

import cls from './RestrictionCorridor.module.scss'

const typeParameters = [
  { value: 'FROM_MAXIMUM', label: 'От максимума' },
  { value: 'FROM_MINIMUM', label: 'От минимума' },
]

interface RestrictionCorridorProps {
  item: IParametersConfigsOutput<IToggleableRestrictionCorridorParameter['value']['value']>
  isEditPlant?: string | boolean
  editModeRole: boolean
  isPastDate: boolean
  changeValue: (type: string, value: ValueConfigsOutput, subType: string) => void
  setEditMode: Dispatch<SetStateAction<boolean>>
}

export const RestrictionCorridor: FC<RestrictionCorridorProps> = (props) => {
  const { item, isEditPlant, editModeRole, isPastDate, changeValue, setEditMode } = props

  return (
    <div className={cls.container}>
      <Select
        disabled={!isEditPlant || !editModeRole || isPastDate}
        variant='outlined'
        label='Тип'
        className={cls.fieldType}
        items={typeParameters}
        value={
          item?.parameterValue?.value?.value ? String(item?.parameterValue?.value?.value?.corridorType) : 'FROM_MAXIMUM'
        }
        onChange={(value) => {
          changeValue('RESTRICTION_CORRIDOR', value, 'corridorType')
          setEditMode(true)
        }}
      />
      <TextField
        disabled={!isEditPlant || !editModeRole || isPastDate}
        type='number'
        label='Дельта, МВт'
        className={cls.fieldDelta}
        toFixed={3}
        positiveNumber
        value={item?.parameterValue?.value?.value?.delta ? String(item.parameterValue.value.value.delta) : ''}
        onChange={(e) => {
          const value = e.target.value
          changeValue('RESTRICTION_CORRIDOR', value, 'delta')
          setEditMode(true)
        }}
      />
      <TextField
        disabled={!isEditPlant || !editModeRole || isPastDate}
        label='Комментарий'
        className={cls.fieldComment}
        value={item?.parameterValue?.value?.value?.comment ? String(item.parameterValue.value.value.comment) : ''}
        onChange={(e) => {
          const value = e.target.value
          changeValue('RESTRICTION_CORRIDOR', value, 'comment')
          setEditMode(true)
        }}
      />
    </div>
  )
}
