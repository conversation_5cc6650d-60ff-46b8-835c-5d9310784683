import { observer } from 'mobx-react'
import { Dispatch, SetStateAction } from 'react'
import { IPlantForLeftMenu, IPrepareRgu } from 'stores/CalcModelStore'
import { useStore } from 'stores/useStore'

import { Station } from './ui/Station'
import { VaultSettings } from './ui/VaultSettings'

export interface IPropsRight {
  editMode: boolean
  setEditMode: Dispatch<SetStateAction<boolean>>
  rows: IPrepareRgu[]
  setRows: Dispatch<SetStateAction<IPrepareRgu[]>>
  isLoading: boolean
  setIsLoading: Dispatch<SetStateAction<boolean>>
  errors: Map<string, string>
  setErrors: Dispatch<SetStateAction<Map<string, string>>>
  isLoadingTable: boolean
  setIsLoadingTable: Dispatch<SetStateAction<boolean>>
  finalPlants: IPlantForLeftMenu[]
  isEditRows: boolean
  init: () => void
  editModeRole: boolean
  plants: IPlantForLeftMenu[]
}

export const RightContainer = observer((props: IPropsRight) => {
  const { editModeRole } = props
  const {
    calcModelStore: { listOfStationsStore },
  } = useStore()
  const { selectedPlantId } = listOfStationsStore

  if (selectedPlantId === 0) {
    return <VaultSettings editModeRole={editModeRole} />
  } else {
    return <Station {...props} />
  }
})
