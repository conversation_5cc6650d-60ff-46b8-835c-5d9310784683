export const validate = (values: any, optimization: string) => {
  const errors = new Map()

  for (const param of values) {
    switch (param.parameterName) {
      case 'GENERATOR_JOINT_WORK_WATCH':
        if (param.parameterValue.value.turnedOn && !param.parameterValue.value.value?.groups?.length) {
          errors.set(
            param.parameterName,
            "Параметр 'Учет связанной работы ГГ' должен содержать хотя бы одну группу генераторов",
          )
        }
        break
      case 'LOAD_UNLOAD_SPEED':
        if (param.parameterValue.value?.turnedOn) {
          const loadSpeed = param.parameterValue.value.value?.loadSpeed
          const unloadSpeed = param.parameterValue.value.value?.unloadSpeed
          if (!loadSpeed && !unloadSpeed) {
            errors.set(
              param.parameterName,
              "Параметр 'Допустимая скорость изменения нагрузки между часами' должен содержать значения скорости набора или снижения нагрузки",
            )
          } else if (loadSpeed.length > 0 && loadSpeed === 0) {
            errors.set(param.parameterName, 'Значение скорости набора нагрузки должно быть больше нуля')
          } else if (unloadSpeed.length > 0 && unloadSpeed === 0) {
            errors.set(param.parameterName, 'Значение скорости снижения нагрузки должно быть больше нуля')
          }
        }
        break
      case 'PARTICIPATION_NPRCH':
        if (
          param.parameterValue.value.turnedOn &&
          (!param.parameterValue.value.value ||
            param.parameterValue.value.value === '' ||
            param.parameterValue.value.value === '0')
        ) {
          errors.set(param.parameterName, "Параметр 'Участие в НПРЧ' должен содержать абсолютное значение")
        }
        break
      case 'PARTICIPATION_AVRCHM':
        if (
          param.parameterValue.value.turnedOn &&
          (!param.parameterValue.value.value ||
            param.parameterValue.value.value === '' ||
            param.parameterValue.value.value === '0')
        ) {
          errors.set(param.parameterName, "Параметр 'Участие в АВРЧМ' должен содержать абсолютное значение")
        }
        break
      case 'RGU_GROUP': {
        if (!optimization && param.parameterValue.value.turnedOn) {
          errors.set(param.parameterName, 'Группа РГЕ может быть включена только для оптимизируемых ГЭС')
        }
        if (param.parameterValue.value.turnedOn && param.parameterValue.value.value === '') {
          errors.set(param.parameterName, "Параметр 'Группа РГЕ' должен содержать номер группы")
        }
        if (
          param.parameterValue.value.turnedOn &&
          (parseInt(param.parameterValue.value.value) < 1001 || parseInt(param.parameterValue.value.value) > 10999)
        ) {
          errors.set(param.parameterName, "Параметр 'Группа РГЕ' допустимый диапазон ввода: 1001 - 10999")
        }
        if (param.parameterValue.value.turnedOn && param.parameterValue.value.value?.toString().includes('.')) {
          errors.set(param.parameterName, 'Номер группы может быть только целым числом')
        }
        break
      }
      case 'MINIMUM_LIMIT':
        if (
          param.parameterValue.value.turnedOn &&
          (!param.parameterValue.value.value?.restrictions.length ||
            !param.parameterValue.value.value?.restrictions.some((item: any) => item.active))
        ) {
          errors.set(
            param.parameterName,
            "Параметр 'Ограничение минимума' должен содержать как минимум одно ограничение",
          )
        }
        break
      case 'MAXIMUM_LIMIT':
        if (
          param.parameterValue.value.turnedOn &&
          (!param.parameterValue.value.value?.restrictions.length ||
            !param.parameterValue.value.value?.restrictions.some((item: any) => item.active))
        ) {
          errors.set(
            param.parameterName,
            "Параметр 'Ограничение максимума' должен содержать как минимум одно ограничение",
          )
        }
        break
      case 'CONSUMPTION_SCHEDULE_BINDING':
        if (param.parameterValue.value.turnedOn && param.parameterValue.value.value === null) {
          errors.set(param.parameterName, "Параметр 'Связь с графиком потребления' должен содержать значение")
        }
        break
      case 'E_MAX_E_MIN':
        if (param.parameterValue.value.turnedOn && !Number(param.parameterValue.value.value)) {
          errors.set(param.parameterName, "Параметр 'Δ(Эмакс-Эмин)' должен содержать абсолютное значение")
        }
        break
      case 'P_GEN_DELTA':
        if (param.parameterValue.value.turnedOn && !Number(param.parameterValue.value.value)) {
          errors.set(param.parameterName, "Параметр 'Δ(Эплан - Σ(Итог.план))' должен содержать абсолютное значение")
        }
        break
      case 'TERTIARY_RESERVE':
        if (param.parameterValue.value.turnedOn && !Number(param.parameterValue.value.value)) {
          errors.set(param.parameterName, "Параметр 'Третичный резерв' должен содержать абсолютное значение")
        }
        break
      case 'PRIORITY_LOAD_RGU':
        if (param.parameterValue.value.turnedOn && param.parameterValue.value.value === '') {
          errors.set(param.parameterName, "Параметр 'Приоритетная загрузка РГЕ' должен содержать значение напряжения")
        }
        break
      case 'PRESSURE_RECESSION_WATCH':
        if (
          param.parameterValue.value.turnedOn &&
          (!param.parameterValue.value.value.generatorPower ||
            !param.parameterValue.value.value.lossPortionRatio ||
            !param.parameterValue.value.value.residualOutput)
        ) {
          errors.set(
            param.parameterName,
            "Параметр 'Учёт снижения напора' должен содержать все значения. Каждое значение должно быть больше 0",
          )
        }
        break
      case 'RESTRICTION_CORRIDOR':
        if (param.parameterValue.value.turnedOn) {
          const corridorErrors = []
          if (!param.parameterValue.value.value.delta) {
            corridorErrors.push("Параметр 'Дельта' должен содержать абсолютное значение")
          }
          if (param.parameterValue.value.value?.comment?.length > 150) {
            corridorErrors.push('Комментарий должен быть не более 150 символов')
          }
          if (corridorErrors.length > 0) {
            errors.set(param.parameterName, corridorErrors.join('. '))
          }
        }
        break
      default:
        break
    }
  }

  return errors
}
