import { TParameterName } from 'entities/api/calcModelPage.entities.ts'
import { observer } from 'mobx-react'
import { useEffect } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { Modal } from 'shared/ui/Modal'
import { useStore } from 'stores/useStore'
import { Table } from 'widgets/Table'

import cls from './HistoryParamsModal.module.scss'

interface IObject {
  id: number
  plantId: number
  type: string
  parameterName: TParameterName
  name: string
  generatorName: string
  rguName?: string
  description?: string
}

export interface HistoryParamsModalProps {
  subtitle: string
  className?: string
  object: IObject
  onClose?: () => void
}

type TRecordType =
  | 'TOGGLEABLE'
  | 'TOGGLEABLE_GENERATOR_GROUPS'
  | 'REGISTRY_TYPE'
  | 'TOGGLEABLE_BIGDECIMAL'
  | 'TOGGLEABLE_ENERGY_DISTRICT'
  | 'TOGGLEABLE_INTEGER'
  | 'TOGGLEABLE_RESTRICTION_LIMIT'
  | 'BIGDECIMAL'
  | 'TOGGLEABLE_PRESSURE_RECESSION'
  | 'TOGGLEABLE_LOAD_UNLOAD_SPEED'

export interface IRecord {
  parameterValue: {
    value: {
      turnedOn: boolean
      value: {
        name: string
        actualLimit: string
        unloadSpeed: string
        loadSpeed: string
        residualOutput: string
        lossPortionRatio: string
        generatorPower: string
        groups: {
          name: string
        }[]
      }
    }
    type: TRecordType
  }
  description: string
}

export interface IRecordRegulatedUnit {
  parameterValue: {
    value: 'PLANT' | 'GENERATOR' | 'RGU'
    type: TRecordType
  }
  description: string
}

const prepareDate = (date: string) => {
  if (date) {
    return date.split('-').reverse().join('.')
  } else {
    return ''
  }
}

export const HistoryParamsModal = observer((props: HistoryParamsModalProps) => {
  const { subtitle, className, onClose, object } = props
  const { calcModelStore } = useStore()
  const { initHistory, initHistoryGenerator, initHistoryRgu, history } = calcModelStore

  useEffect(() => {
    if (object.type === 'generator') {
      initHistoryGenerator(object.id, object.parameterName)
    } else if (object.type === 'rgu') {
      initHistoryRgu(String(object.id), object.parameterName)
    } else if (object) {
      initHistory(object.plantId, object.parameterName)
    }
  }, [object])

  const coloredText = (enable: boolean) => {
    return (
      <span className={classNames(enable ? cls.EnabledText : cls.DisabledText, {}, [])}>
        {enable ? 'Вкл.' : 'Выкл.'}
      </span>
    )
  }

  const historyValueRegulatedUnit = (record: IRecordRegulatedUnit) => {
    switch (record.parameterValue.value) {
      case 'PLANT':
        return 'Электростанция'
      case 'GENERATOR':
        return 'Гидрогенератор'
      case 'RGU':
        return 'РГЕ'
    }
  }
  const historyValueOther = (record: IRecord) => {
    switch (record.parameterValue.type) {
      case 'TOGGLEABLE':
        return coloredText(record?.parameterValue?.value?.turnedOn)
      case 'TOGGLEABLE_GENERATOR_GROUPS':
        return (
          <>
            <>{coloredText(record.parameterValue.value.turnedOn)}</>
            <span>
              {record.parameterValue.value.value
                ? ` (${record.parameterValue.value.value.groups?.map((group) => group.name).join(', ')})`
                : ''}
            </span>
          </>
        )
      case 'REGISTRY_TYPE':
        return record.parameterValue.value
      case 'TOGGLEABLE_BIGDECIMAL':
        return (
          <>
            <>{coloredText(record?.parameterValue?.value?.turnedOn ?? false)}</>
            <span>{record.parameterValue.value.value ? ` (${record.parameterValue.value.value})` : ''}</span>
          </>
        )
      case 'TOGGLEABLE_ENERGY_DISTRICT':
        return (
          <>
            <>{coloredText(record.parameterValue.value.turnedOn)}</>
            <span>{record.parameterValue.value.value?.name ? ` (${record.parameterValue.value.value.name})` : ''}</span>
          </>
        )
      case 'TOGGLEABLE_INTEGER':
        return (
          <>
            <>{coloredText(record.parameterValue.value.turnedOn)}</>
            <span>{record.parameterValue.value.value ? ` (${record.parameterValue.value.value})` : ''}</span>
          </>
        )
      case 'TOGGLEABLE_RESTRICTION_LIMIT':
        return (
          <>
            <>{coloredText(record.parameterValue.value.turnedOn)}</>
            <span>
              {record.parameterValue.value.value ? ` (${record.parameterValue.value.value.actualLimit ?? ''})` : ''}
            </span>
          </>
        )
      case 'TOGGLEABLE_LOAD_UNLOAD_SPEED':
        return (
          <>
            <>{coloredText(record.parameterValue.value.turnedOn)}</>
            <span>
              {record.parameterValue.value.value
                ? ` (${record.parameterValue.value.value.loadSpeed ?? ''}/${
                    record.parameterValue.value.value.unloadSpeed ?? ''
                  })`
                : ''}
            </span>
          </>
        )
      case 'BIGDECIMAL':
        return record.parameterValue.value
      case 'TOGGLEABLE_PRESSURE_RECESSION':
        return (
          <>
            {coloredText(record.parameterValue.value.turnedOn)}
            <span>
              {record.parameterValue.value.value
                ? ` (${record.parameterValue.value.value.residualOutput ?? ''}/${
                    record.parameterValue.value.value.lossPortionRatio ?? ''
                  }/${record.parameterValue.value.value.generatorPower ?? ''})`
                : ''}
            </span>
          </>
        )
      default:
        return record.description
    }
  }

  const historyValue = (record: IRecord & IRecordRegulatedUnit) => {
    if (object.parameterName === 'REGULATED_UNIT') {
      return historyValueRegulatedUnit(record)
    } else if (object.parameterName === 'E_MAX_E_MIN') {
      const value = record?.parameterValue?.value?.value

      return (
        <>
          <>{coloredText(record?.parameterValue?.value?.turnedOn ?? false)}</>
          <span>{value ? `(${Number(value) / 1000})` : ''}</span>
        </>
      )
    } else {
      return historyValueOther(record)
    }
  }

  const columns = [
    {
      name: 'description',
      title: 'Значение',
      width: 200,
      render: (_: unknown, record: IRecord & IRecordRegulatedUnit) => {
        return <>{historyValue(record)}</>
      },
    },
    {
      name: 'startDate',
      title: 'Дата начала',
      width: 200,
      render: (value: string) => {
        return <>{prepareDate(value) || '-'}</>
      },
    },
    {
      name: 'endDate',
      title: 'Дата окончания',
      width: 200,
      render: (value: string) => {
        return <>{prepareDate(value) || '-'}</>
      },
    },
  ]

  const getDisplayName = () => {
    if (object.type === 'rgu') {
      // Для РГЕ возвращаем только rguName, так как subtitle уже содержится в нем
      return object.rguName ?? ''
    }

    if (object.type === 'generator') {
      // Для генераторов возвращаем subtitle + generatorName
      return `${subtitle} ${object.generatorName ?? ''}`.trim()
    }

    // Для параметров станции возвращаем только subtitle
    return subtitle ?? ''
  }

  const displayName = getDisplayName()

  return (
    <Modal
      maxWidth='lg'
      title='История параметра'
      onClose={onClose}
      className={classNames(cls.HistoryParamsModal, {}, className ? [className] : [])}
      skipConfirmOnClose
    >
      <div className={cls.subtitle}>{`${object?.name ?? ''} (${displayName})`}</div>
      <div className={classNames(cls.Body, {}, [])}>
        <Table columns={columns} rows={history} height={350} />
      </div>
    </Modal>
  )
})
