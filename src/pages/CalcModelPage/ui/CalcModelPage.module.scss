.CalcModelPage {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.Header {
  height: var(--header-page);
  background-color: var(--background-color-secondary);
  border-radius: 8px;
  box-shadow: var(--shadow-page);
  padding: 0 16px;
}

.Body {
  border-radius: 8px;
  background-color: var(--background-color-secondary);
  //box-shadow: var(--shadow-page);
  margin-top: 0.2rem;
  height: 100%;
  overflow: hidden;
  width: 100%;
}
