import { Tooltip } from '@mui/material'
import { addDays, format } from 'date-fns'
import { observer } from 'mobx-react'
import { useActiveTabLogic } from 'pages/CalcModelWerPage/ui/ListOfStations/hooks/useActiveTabLogic'
import { useEffect, useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { classNames } from 'shared/lib/classNames'
import { locationParse } from 'shared/lib/locationParse'
import { Button } from 'shared/ui/Button'
import { CheckEditComponent } from 'shared/ui/CheckEditComponent'
import { DatePicker } from 'shared/ui/DatePicker'
import { Icon } from 'shared/ui/Icon'
import { MenuDragAndDrop } from 'shared/ui/MenuDragAndDrop'
import { CurrentItemProps } from 'shared/ui/MenuDragAndDrop/MenuDragAndDrop'
import { SettingStationsForWatch } from 'shared/ui/SettingStationsForWatch/SettingStationsForWatch.tsx'
import { TextField } from 'shared/ui/TextField'
import { useStore } from 'stores/useStore'

import cls from './SidePanel.module.scss'

type TSortType = 'alphabeat' | 'custom'

export const SidePanel = observer(() => {
  const { year = null, month = null, day = null } = locationParse(location.search)
  const history = useNavigate()
  const { calcModelWerStore } = useStore()
  const {
    listOfStationsStore: { activeTab, editMode },
    plants,
    date,
    setDate,
    setCustomSortLeft,
    changeSortType,
    depPlants,
    initModalAdd,
    lookedPlants,
    saveDepPlants,
    resetLookedPlants,
    setSelectedPlant,
    selectedPlant,
  } = calcModelWerStore
  const { isModified } = useActiveTabLogic(activeTab.value)

  const nowDate = addDays(new Date(), 1)
  const initDate = year && month && day ? new Date(`${year}-${month}-${day}`) : nowDate
  const [isAddModal, setIsAddModal] = useState(false)
  const [search, setSearch] = useState('')
  const [typeSort, setTypeSort] = useState<TSortType>('custom')
  const [_, setErrors] = useState(() => new Map())

  const finalPlants = useMemo(() => {
    return plants.filter((el) => {
      return el?.label?.toUpperCase()?.includes(search?.toUpperCase())
    })
  }, [plants, search])

  const customCell = (item: CurrentItemProps) => {
    const isView = item.icon === 'view'

    return (
      <div
        className={classNames(
          cls.dragMenuCell,
          {
            [cls.view]: isView,
            [cls.select]: selectedPlant?.value === item.value,
          },
          [],
        )}
      >
        <div className={cls.customLabel}>
          <Tooltip title={item.label.length > 19 ? item.label : null}>
            <>{item.label}</>
          </Tooltip>
        </div>
      </div>
    )
  }

  const onChangeDateHandler = (date: Date) => {
    setDate(date)
    getDataLeft(typeSort, date)
    history(`?year=${date.getFullYear()}&month=${date.getMonth() + 1}&day=${date.getDate()}`)
  }

  const getDataLeft = (sortType: TSortType, newDate?: Date) => {
    const d = newDate || date
    const preparedDate = format(d, 'yyyy-MM-dd')
    changeSortType(sortType !== 'alphabeat', preparedDate)
  }

  const onChangeSortType = (sortType: TSortType) => {
    if (typeSort !== sortType) {
      setTypeSort(sortType)
      getDataLeft(sortType)
    }
  }

  useEffect(() => {
    onChangeDateHandler(initDate)

    return () => {
      calcModelWerStore.resetPlants()
      setDate(initDate)
    }
  }, [])

  return (
    <CheckEditComponent isEdit={isModified}>
      <div className={cls.left}>
        <div className={cls.headerLeft}>
          <DatePicker
            className={cls.datePicker}
            disabled={isModified}
            value={date}
            setValue={onChangeDateHandler}
            isArrow
            isDayOfWeek
          />
          {editMode && (
            <Button
              className={cls.buttonSettingStation}
              variant='outlined'
              onClick={() => {
                setIsAddModal(true)
              }}
            >
              Настройка станций
            </Button>
          )}
        </div>
        <div className={cls.searchContainer}>
          <TextField
            className={cls.searchInput}
            variant='standard'
            value={search}
            placeholder='Поиск'
            onChange={(e) => {
              setSearch(e.target.value)
            }}
          />
          <div className={cls.buttons}>
            <Button
              variant='text'
              className={classNames(
                cls.searchButton,
                {
                  [cls.selectSort]: typeSort === 'alphabeat',
                  [cls.unSelectSort]: typeSort === 'custom',
                },
                [],
              )}
              onClick={() => onChangeSortType('alphabeat')}
            >
              <Icon width={18} name='sortAlphabeat' />
            </Button>
            <Button
              variant='text'
              className={classNames(
                cls.searchButton,
                {
                  [cls.selectSort]: typeSort === 'custom',
                  [cls.unSelectSort]: typeSort === 'alphabeat',
                },
                [],
              )}
              onClick={() => onChangeSortType('custom')}
            >
              <Icon width={18} name='sortCustom' />
            </Button>
          </div>
        </div>
        <div className={cls.dragAndDropContainer}>
          <MenuDragAndDrop
            select={selectedPlant?.value ?? null}
            isViewActions={false}
            isEdit={isModified}
            setSelect={(v) => {
              setErrors((prev) => {
                prev.clear()

                return prev
              })
              setSelectedPlant(v as null | number)
            }}
            items={finalPlants as CurrentItemProps[]}
            customCell={customCell}
            disabled={typeSort === 'alphabeat' || !editMode}
            typeSort={typeSort}
            isDragMode
            onChangePosition={async (res) => {
              await setCustomSortLeft(res)
            }}
          />
        </div>
      </div>
      {isAddModal && (
        <SettingStationsForWatch
          onClose={() => {
            setIsAddModal(false)
          }}
          onSave={() => {
            setIsAddModal(false)
            getDataLeft(typeSort)
          }}
          depPlants={depPlants}
          initModalAdd={initModalAdd}
          lookedPlants={lookedPlants}
          saveDepPlants={saveDepPlants}
          resetLookedPlants={resetLookedPlants}
        />
      )}
    </CheckEditComponent>
  )
})
