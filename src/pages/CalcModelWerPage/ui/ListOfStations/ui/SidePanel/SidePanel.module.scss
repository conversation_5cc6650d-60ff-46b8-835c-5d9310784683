.dragMenuCell {
  display: flex;
  overflow: hidden;
  width: 100%;
}

.view {
  color: var(--text-gray) !important;
}

.select {
  color: var(--primary-color) !important;
}

.customLabel {
  font-size: 14px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 150px;
}

.left {
  flex-shrink: 0;
  height: 100%;
  width: 14rem;
  min-width: 14rem;
  max-width: 14rem;
  background-color: var(--background-color-secondary);
  border-radius: 8px;
  box-shadow:
    0 2px 3px 0 rgb(0 0 0 / 5%),
    0 1px 2px 0 rgb(0 0 0 / 10%);
  padding: 0.2rem;
}

.headerLeft {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  width: 100%;
}

.datePicker {
  width: 130px;
  height: 24px;
  border-radius: 4px;

  & > div {
    height: 26px;
  }
}

.buttonSettingStation {
  margin-top: 6px !important;
  height: 26px;
}

.searchContainer {
  display: flex;
  margin: 16px 8px 10px;
  height: 24px;
}

.searchInput {
  height: 18px;
  width: 100%;

  & > div > input {
    padding: 0 !important;
  }
}

.buttons {
  margin-left: auto;
  width: 60px;
}

.searchButton {
  height: 22px;
  width: 22px !important;
  min-width: 22px !important;
  padding: 0 !important;
}

.selectSort {
  color: var(--primary-color) !important;
}

.unSelectSort {
  color: var(--text-gray) !important;
}

.dragAndDropContainer {
  width: 100%;
  // высота родителя - высота поля поиска и поля выбора даты - padding родительского элемента
  height: calc(100% - 98px - 0.4rem);
  overflow: auto;
}
