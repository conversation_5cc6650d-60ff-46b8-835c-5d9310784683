import { observer } from 'mobx-react'
import { Select } from 'shared/ui/Select'
import { useStore } from 'stores/useStore'

import cls from './TailraceDownstreamSelector.module.scss'

export const TailraceDownstreamSelector = observer(() => {
  const {
    calcModelWerStore: {
      formattedDate,
      isSelectedDateEditable,
      selectedPlant,
      isSelectedPlantViewOnly,
      listOfStationsStore: {
        characteristicsStore: {
          editMode,
          tailraceStore: {
            downstreamPlantList,
            getTailraceCharacteristics,
            selectedDownstreamPlant,
            setSelectedDownstreamPlant,
            isEditRows,
          },
        },
      },
    },
  } = useStore()

  const isSelectorDisabled = !isSelectedDateEditable || isSelectedPlantViewOnly || !editMode || isEditRows

  const handleChange = (value: string | number) => {
    const selectedId = typeof value === 'string' ? Number(value) : value
    const selectedStation = downstreamPlantList.find((station) => station.plantId === selectedId)
    if (selectedStation) {
      setSelectedDownstreamPlant(selectedStation)
      if (selectedPlant) {
        getTailraceCharacteristics(selectedPlant.plantId, formattedDate, selectedStation, true)
      }
    }
  }

  if (downstreamPlantList.length > 0) {
    const items = downstreamPlantList.map((station) => ({
      label: station.name,
      value: station.plantId,
    }))

    return (
      <div className={cls.container}>
        <p>Нижележащая ГЭС</p>
        <Select
          variant='outlined'
          disabled={isSelectorDisabled}
          items={items}
          onChange={handleChange}
          value={selectedDownstreamPlant?.plantId ?? ''}
          className={cls.selector}
        />
      </div>
    )
  }

  return null
})
