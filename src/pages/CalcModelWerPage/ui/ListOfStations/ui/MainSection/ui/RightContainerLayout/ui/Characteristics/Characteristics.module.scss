.container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 15px 30px 0 30px;
  overflow: auto;
}

.noData {
  background-color: var(--background-color-secondary);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  color: var(--text-gray);
}

.tabs {
  display: flex;
  align-items: center;
  justify-content: center;
}
