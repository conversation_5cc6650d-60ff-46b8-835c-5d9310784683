import { observer } from 'mobx-react'
import { Toggle } from 'shared/ui/Toggle'
import { useStore } from 'stores/useStore'

import cls from './Characteristics.module.scss'
import { ReservoirVolumes } from './ui/ReservoirVolumes'
import { SpecificConsumption } from './ui/SpecificConsumption'
import { Tailrace } from './ui/Tailrace'

export const Characteristics = observer(() => {
  const { calcModelWerStore } = useStore()
  const {
    selectedPlant,
    listOfStationsStore: {
      characteristicsStore: { subTabs, activeSubTab, setActiveSubTab },
    },
  } = calcModelWerStore

  if (!selectedPlant) return <div className={cls.noData}>Выберите станцию</div>

  return (
    <div className={cls.container}>
      <div className={cls.tabs}>
        <Toggle items={subTabs} value={activeSubTab} setValue={setActiveSubTab} />
      </div>
      {activeSubTab === 'reservoirVolumes' && <ReservoirVolumes />}
      {activeSubTab === 'tailwaterLevelConsumptionRelations' && <Tailrace />}
      {activeSubTab === 'specificConsumption' && <SpecificConsumption />}
    </div>
  )
})
