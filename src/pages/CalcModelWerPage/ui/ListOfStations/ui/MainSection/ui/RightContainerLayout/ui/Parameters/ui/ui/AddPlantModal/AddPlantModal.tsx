import { format } from 'date-fns'
import { observer } from 'mobx-react'
import { useEffect, useState } from 'react'
import { prepareFlatData } from 'shared/lib/prepareData'
import { Button } from 'shared/ui/Button'
import { Modal } from 'shared/ui/Modal'
import { IWerDepPlantsRow } from 'stores/CalcModelWerStore/WerListOfStationsStore/WerParametersStore/IWerParametersStore.types'
import { useStore } from 'stores/useStore'
import { Table } from 'widgets/Table'

import cls from './AddPlantModal.module.scss'

interface AddPlantModalProps {
  onClose: () => void
  plantType: 'upstreamPlants' | 'downstreamPlants'
}

export const AddPlantModal = observer((props: AddPlantModalProps) => {
  const { onClose, plantType } = props
  const { calcModelWerStore } = useStore()
  const {
    listOfStationsStore: { parametersStore },
  } = calcModelWerStore
  const { depPlants, getDepartmentHierarchy, isLoadingDepartmentHierarchy, addNewPlantRelation } = parametersStore

  const [selected, setSelected] = useState<string | null>(null)
  const [expandedRowIds, setExpandedRowIds] = useState<string[]>([])

  useEffect(() => {
    getDepartmentHierarchy()
  }, [getDepartmentHierarchy])

  // Предварительно разворачиваем строки в ЦДУ в модальном окне добавления станции
  useEffect(() => {
    const flatData = prepareFlatData<IWerDepPlantsRow>(depPlants)
    const initialExpandedIds = flatData.reduce<string[]>((acc, el) => {
      if (el.name.toUpperCase().includes('ЦДУ')) {
        acc.push(el.tabId)
      }

      return acc
    }, [])
    setExpandedRowIds(initialExpandedIds)
  }, [depPlants])

  const title = plantType === 'upstreamPlants' ? 'Добавление вышележащей станции' : 'Добавление нижележащей станции'

  const columns = [
    {
      name: 'name',
      title: 'Название',
      width: 440,
      render: (val: string) => <div>{val}</div>,
    },
  ]

  const handleAdd = () => {
    if (!selected) return

    const selectedPlant = findPlantById(depPlants, selected)

    if (selectedPlant && selectedPlant.type === 'PLANT') {
      const newPlant = {
        plantId: selectedPlant.id,
        plantName: selectedPlant.name,
        travelTime: null,
        werDepartments: [],
        startDate: format(new Date(), 'yyyy-MM-dd'),
        endDate: '',
        affluent: false,
      }
      addNewPlantRelation(plantType, newPlant)
      onClose()
    }
  }

  // Рекурсивно ищем станцию по id в иерархическом дереве
  const findPlantById = (plants: IWerDepPlantsRow[], id: string): IWerDepPlantsRow | null => {
    for (const plant of plants) {
      if (plant.tabId === id) return plant
      if (plant.children) {
        const found = findPlantById(plant.children, id)
        if (found) return found
      }
    }

    return null
  }

  const showAddButton = selected !== null

  return (
    <Modal
      onClose={onClose}
      title={title}
      className={cls.modal}
      actions={showAddButton && <Button onClick={handleAdd}>Добавить</Button>}
      skipConfirmOnClose
    >
      <Table
        showSelectAll={false}
        columns={columns}
        rows={depPlants}
        childKey='name'
        selectMode='many'
        selection={selected ? [selected] : []}
        setSelection={(sel) => setSelected((sel as string[])[0] || null)}
        initSearchMode
        isForceSearch
        expandedRowIds={expandedRowIds}
        setExpandenRowIds={setExpandedRowIds}
        loading={isLoadingDepartmentHierarchy}
        tableType='nsi'
      />
    </Modal>
  )
})
