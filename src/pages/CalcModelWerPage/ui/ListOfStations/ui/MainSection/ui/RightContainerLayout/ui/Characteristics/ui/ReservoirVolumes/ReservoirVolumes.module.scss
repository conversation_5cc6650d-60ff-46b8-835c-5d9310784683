.container {
  display: grid;
  grid-template-columns: 280px 1fr;
  grid-template-rows: minmax(0, 60px) 1fr;
  gap: 10px;
  width: 100%;
  height: 100%;
}

.leftSection {
  grid-column: 1;
  grid-row: 1 / span 2;
  display: flex;
  flex-direction: column;
  gap: 10px;

  &Settings {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}

.rightSection {
  grid-column: 2;
  grid-row: 2;
  height: 100%;
  min-width: 480px;
}
