import { Icon<PERSON><PERSON>on, Tooltip } from '@mui/material'
import { format, startOfDay } from 'date-fns'
import { IWerDepartment } from 'entities/api/calcModelWerManager.entities'
import { observer } from 'mobx-react'
import {
  validatePlantEndDate,
  validatePlantStartDate,
} from 'pages/CalcModelWerPage/ui/ListOfStations/ui/MainSection/ui/RightContainerLayout/ui/Parameters/lib/validatePlantDate.ts'
import { FC, useEffect, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { prepareDate } from 'shared/lib/prepareData'
import { Icon } from 'shared/ui/Icon'
import { useStore } from 'stores/useStore'
import { IBaseRowData, IColumn, TableV1 } from 'widgets/TableV1'

import { renderWerDepartments } from '../../lib/renderFuctions'
import { AddPlantModal } from '../ui/AddPlantModal'
import { UpDownStreamTableCell } from '../ui/UpDownStreamTableCell'
import cls from './DownstreamPlants.module.scss'

interface IDownstreamPlantsRow extends IBaseRowData {
  plantName: string
  travelTime: number | null
  departmentName: IWerDepartment[]
  startDate: string
  endDate: string | null
  affluent?: boolean
  type: string
}

interface Props {
  isEditingEnabled: boolean
}

export const DownstreamPlants: FC<Props> = observer(({ isEditingEnabled }) => {
  const { calcModelWerStore } = useStore()
  const {
    listOfStationsStore: {
      parametersStore: { plantCascades, updateAffluentValue, editMode, updatePlantDate, removePlant },
    },
  } = calcModelWerStore

  // Функция-костыль для синхронизации рендера таблицы с изменениями в сторе
  const mapPlantData = () =>
    plantCascades?.downstreamPlants?.map((plant) => ({
      tabId: plant.plantId,
      plantName: plant.plantName,
      travelTime: null, // Данные для времени добегания пока бек не дает
      departmentName: plant.werDepartments,
      startDate: plant.startDate,
      endDate: plant.endDate ? plant.endDate : null,
      affluent: plant.affluent,
      type: 'ADDED', // Костыль, чтобы можно было редактировать startDate
    })) || []

  const initialRows: IDownstreamPlantsRow[] = mapPlantData()

  const [rows, setRows] = useState(initialRows)
  const [isOpenAddNewPlantModal, setIsOpenAddNewPlantModal] = useState(false)

  useEffect(() => {
    setRows(initialRows)
  }, [plantCascades?.downstreamPlants])

  const baseColumns: IColumn<IDownstreamPlantsRow>[] = [
    {
      name: 'plantName',
      title: 'Станция',
      width: 200,
      render: (value: IDownstreamPlantsRow['plantName']) => <UpDownStreamTableCell value={value} />,
    },
    {
      name: 'travelTime',
      title: 'tдоб, дни',
      width: 100,
      render: (value: IDownstreamPlantsRow['travelTime']) => <UpDownStreamTableCell value={value} />,
    },
    {
      name: 'departmentName',
      title: 'ДЦ ВЭР',
      width: 180,
      render: renderWerDepartments,
    },
    {
      name: 'startDate',
      title: 'Дата начала связи',
      width: 150,
      editing: {
        type: 'date',
        enabled: true,
        showErrorMessage: false,
        onAfterChange: (val, row) => {
          updatePlantDate('downstreamPlants', row.tabId, 'startDate', val)
        },
        onValid: (startDate, row) =>
          validatePlantStartDate(startDate, row.endDate ? startOfDay(new Date(row.endDate)) : null),
      },
      getCellRenderValue: (value: IDownstreamPlantsRow['startDate']) => format(new Date(value), 'dd.MM.yyy'),
      render: (value: IDownstreamPlantsRow['startDate'], _, defaultClassName) => (
        <UpDownStreamTableCell value={prepareDate(value)} className={defaultClassName} />
      ),
    },
    {
      name: 'endDate',
      title: 'Дата окончания связи',
      width: 180,
      editing: {
        type: 'date',
        enabled: true,
        showErrorMessage: false,
        onAfterChange: (val, row) => {
          updatePlantDate('downstreamPlants', row.tabId, 'endDate', val)
        },
        onValid: (endDate, row) =>
          validatePlantEndDate(endDate, row.startDate ? startOfDay(new Date(row.startDate)) : null),
      },
      canClearCell: true,
      getCellRenderValue: (value: IDownstreamPlantsRow['endDate']) =>
        value ? format(new Date(value), 'dd.MM.yyy') : '',
      render: (value: IDownstreamPlantsRow['endDate'], _, defaultClassName) => (
        <UpDownStreamTableCell value={value ? prepareDate(value) : ''} className={defaultClassName} />
      ),
    },
    {
      name: 'affluent',
      title: 'Подпор',
      editing: {
        type: 'switch',
        enabled: isEditingEnabled,
        onAfterChange: (value: IDownstreamPlantsRow['affluent'], row: IDownstreamPlantsRow) => {
          updateAffluentValue(row.tabId, value as boolean)
        },
      },
      width: 80,
    },
  ]

  const actionColumn = {
    name: 'action',
    title: '',
    width: 50,
    headRender: () => {
      return (
        <div className={cls.actionHeader}>
          <Tooltip title='Добавить связь'>
            <span>
              <IconButton
                sx={{
                  color: 'var(--primary-color)',
                  display: 'inline-flex!important',
                  padding: 0,
                }}
                className={cls.addIcon}
                onClick={() => {
                  setIsOpenAddNewPlantModal(true)
                }}
                disabled={!isEditingEnabled}
              >
                <Icon name='plus' width={13} />
              </IconButton>
            </span>
          </Tooltip>
        </div>
      )
    },
    render: (_: unknown, row: IDownstreamPlantsRow) => {
      return (
        <div className={cls.actionsWrapper}>
          <div className={cls.iconCell}>
            <Tooltip title='Удалить'>
              <span>
                <IconButton
                  onClick={() => {
                    removePlant('downstreamPlants', row.tabId)
                    setRows(mapPlantData())
                  }}
                  className={classNames('', { [cls.iconRedBtn]: isEditingEnabled })}
                  disabled={!isEditingEnabled}
                >
                  <Icon name='trash' width={13} height={13} />
                </IconButton>
              </span>
            </Tooltip>
          </div>
        </div>
      )
    },
  }

  const columns = (editMode ? [...baseColumns, actionColumn] : baseColumns) as IColumn<IDownstreamPlantsRow>[]

  return (
    <div className={cls.container}>
      <div className={cls.title}>Нижележащая ГЭС</div>
      <TableV1
        setRows={setRows}
        columns={columns}
        rows={rows}
        editMode={isEditingEnabled}
        className={cls.table}
        maxVisibleRows={2}
        rowHeight='auto'
      />
      {isOpenAddNewPlantModal && (
        <AddPlantModal onClose={() => setIsOpenAddNewPlantModal(false)} plantType='downstreamPlants' />
      )}
    </div>
  )
})
