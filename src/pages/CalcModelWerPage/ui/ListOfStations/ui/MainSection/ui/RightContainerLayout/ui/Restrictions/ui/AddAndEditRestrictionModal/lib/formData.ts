import { IWerRestrictionOptions } from 'entities/api/calcModelPage.entities.ts'
import { EMPTY_VALUE } from 'pages/ReportsPage/ui/ModalCreateEditReport/config/const.ts'
import { Values } from 'pages/ReportsPage/ui/ModalCreateEditReport/config/types.ts'
import { parseISO8601DateMonth } from 'shared/lib/dateFormates'
import { IWerRestrictionPrePost } from 'stores/CalcModelWerStore/WerListOfStationsStore/WerRestrictionsStore/WerRestrictionsStore.types'

export const getInitialValues = (options: IWerRestrictionOptions | null): Values => ({
  parameter: options?.parameters
    ? { newValue: options.parameters[0].value, oldValue: options.parameters[0].value }
    : EMPTY_VALUE,
  category: options?.categories
    ? { newValue: options.categories[0].value, oldValue: options.categories[0].value }
    : EMPTY_VALUE,
  type: options?.types ? { newValue: options.types[0].value, oldValue: options.types[0].value } : EMPTY_VALUE,
  beginDate: EMPTY_VALUE,
  stopDate: EMPTY_VALUE,
  minValue: EMPTY_VALUE,
  maxValue: EMPTY_VALUE,
  valueType: EMPTY_VALUE,
  comment: EMPTY_VALUE,
})

export const toInitialValue = (restriction: IWerRestrictionPrePost): Values => {
  let beginDateValue: Date | null = null
  if (restriction.category?.code === 'TEMPORARY' && restriction.temporaryBeginDate) {
    beginDateValue = new Date(restriction.temporaryBeginDate)
  } else if (restriction?.beginDate) {
    beginDateValue = parseISO8601DateMonth(restriction.beginDate)
  }
  let stopDateValue: Date | null = null
  if (restriction.category?.code === 'TEMPORARY' && restriction.temporaryEndDate) {
    stopDateValue = new Date(restriction.temporaryEndDate)
  } else if (restriction?.stopDate) {
    stopDateValue = parseISO8601DateMonth(restriction.stopDate)
  }

  return {
    parameter: {
      oldValue: restriction.parameter.code,
      newValue: restriction.parameter.code,
    },
    category: {
      oldValue: restriction.category.code,
      newValue: restriction.category.code,
    },
    type: {
      oldValue: restriction.type.code,
      newValue: restriction.type.code,
    },
    beginDate: {
      oldValue: beginDateValue,
      newValue: beginDateValue,
    },
    stopDate: {
      oldValue: stopDateValue,
      newValue: stopDateValue,
    },
    minValue: {
      oldValue: restriction.minValue,
      newValue: restriction.minValue,
    },
    maxValue: {
      oldValue: restriction.maxValue,
      newValue: restriction.maxValue,
    },
    valueType: {
      oldValue: restriction.valueType ? restriction.valueType.code : null,
      newValue: restriction.valueType ? restriction.valueType.code : null,
    },
    comment: {
      oldValue: restriction.comment,
      newValue: restriction.comment,
    },
  }
}

export const getUnitsMeasurementValue = (key: string) => {
  if (
    [
      'UPSTREAM_LEVEL',
      'DOWNSTREAM_LEVEL',
      'DELTA_UPSTREAM_LEVEL',
      'DELTA_DOWNSTREAM_LEVEL',
      'PRESSURE',
      'DELTA_PRESSURE',
    ].includes(key)
  ) {
    // Уровень ВБ, Уровень НБ, ∆Уровень ВБ, ∆Уровень НБ
    return 'метры'
  } else if (
    ['FLOW', 'TURBINE_FLOW', 'BYPASS_FLOW', 'DELTA_FLOW', 'DELTA_TURBINE_FLOW', 'DELTA_BYPASS_FLOW'].includes(key)
  ) {
    // Расход, Турбинный расход, Расход хол. сбр., ∆Расход, ∆Турбинный расход, ∆Расход хол. сбр.
    return 'м³/сек'
  } else if (['POWER', 'DELTA_POWER'].includes(key)) {
    // Мощность, ∆Мощность
    return 'МВт'
  } else if (['OUTPUT', 'DELTA_OUTPUT'].includes(key)) {
    // Выработка, ∆Выработка
    return 'млн. кВтч'
  }
}
