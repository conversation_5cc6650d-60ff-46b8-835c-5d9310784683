import { Switch } from '@mui/material'
import { observer } from 'mobx-react'
import { Icon } from 'shared/ui/Icon'
import { useStore } from 'stores/useStore'

import cls from './FreezingToggle.module.scss'

export const FreezingToggle = observer(() => {
  const {
    calcModelWerStore: {
      isSelectedDateEditable,
      isSelectedPlantViewOnly,
      listOfStationsStore: {
        characteristicsStore: { editMode, tailraceStore },
      },
    },
  } = useStore()
  const { freezing, isFreezing, toggleFreezing, isEditRows } = tailraceStore

  const isToggleDisabled = !editMode || !isSelectedDateEditable || isSelectedPlantViewOnly || isEditRows

  if (freezing !== undefined) {
    return (
      <div className={cls.toggleContainer}>
        <Icon name='sun' width={20} height={20} className={cls.sunMode} />
        <Switch
          checked={isFreezing}
          onChange={toggleFreezing}
          disabled={isToggleDisabled}
          size='small'
          sx={{
            '& .MuiSwitch-track': {
              backgroundColor: 'rgb(233, 184, 51);',
            },
          }}
        />
        <Icon name='snowflake' width={20} height={20} />
      </div>
    )
  }
})
