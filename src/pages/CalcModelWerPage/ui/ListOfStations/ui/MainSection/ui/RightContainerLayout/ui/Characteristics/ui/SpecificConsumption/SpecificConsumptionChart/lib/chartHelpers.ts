/**
 * Функция предназначена для преобразования исходного двумерного массива данных
 * (каждая строка массива имеет вид [значение для уровня, значение для объема]
 * в массив точек для графика, где каждая точка представлена объектом вида { x, y, rowNumber }:
 *
 * - x: координата из столбца объема водохранилища
 * - y: координата из столбца уровня водохранилища
 * - rowNumber: номер строки
 *
 * При этом, если значение в ячейке отсутствует (то есть оно undefined, пустая строка
 * или иное «ложное» значение), то вместо числа возвращается null. Это делается для того, чтобы:
 *
 * • Highcharts видел, что в данной точке нет данных, и корректно обрабатывал пропуски.
 * • Избежать автоматического преобразования отсутствующего значения в 0, что могло бы исказить график.
 */
export const mapSpreadsheetDataToPoints = (data: (number | string | undefined)[][]) => {
  // Шаг 1. Преобразуем исходный массив в массив объектов.
  const mappedData = data.map((item, index) => {
    const x = item[0] ? Number(item[0]) : null
    const y = item[1] ? Number(item[1]) : null

    return { x, y, rowNumber: index + 1 }
  })

  // Шаг 2. Определяем индекс последней заполненной строки.
  // Здесь считаем строку "заполненной", если хотя бы одно из значений x или y не равно null.
  let lastFilledIndex = mappedData.length - 1
  while (lastFilledIndex >= 0 && mappedData[lastFilledIndex].x === null && mappedData[lastFilledIndex].y === null) {
    lastFilledIndex--
  }

  // Шаг 3. Отсекаем "хвостовые" строки, в которых после последней заполненной строки все значения null,
  // если таковые имеются.
  // Важно! Без данной манипуляции в случае большой выборки данных и наличии пустых строк
  // график "ломается" (пропадает yAxis и сам график). А если отсекать все строки со значениями null, то
  // в случае наличия промежуточных пустых строк, произойдёт слипание графика (последняя до разрыва строка
  // соединится линией с первой после разрыва)
  const trimmedData = mappedData.slice(0, lastFilledIndex + 1)

  return trimmedData
}
