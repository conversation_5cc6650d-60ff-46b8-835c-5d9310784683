import Highcharts, { Point, PointOptionsObject } from 'highcharts'
import { observer } from 'mobx-react'
import { Chart } from 'shared/ui/Chart'
import { useStore } from 'stores/useStore'

import { getFormattedUnit } from '../../../lib'
import { mapSpreadsheetDataToPoints } from './lib'

interface CustomPoint extends Point {
  rowNumber?: number
}

export const SpecificConsumptionChart = observer(() => {
  const { calcModelWerStore } = useStore()
  const { listOfStationsStore } = calcModelWerStore
  const { characteristicsStore } = listOfStationsStore
  const { specificConsumptionStore } = characteristicsStore
  const { isCharacteristicsLoaded, currentSettings, specificConsumptionSpreadsheetData, actualData } =
    specificConsumptionStore

  if (!isCharacteristicsLoaded || !currentSettings) {
    return null
  }

  const unit = getFormattedUnit(currentSettings.unit)
  // Приводим к типу PointOptionsObject, так как Highcharts по умолчанию не ожидает увидеть null значения для точек
  const lineSeriesData = mapSpreadsheetDataToPoints(specificConsumptionSpreadsheetData.data) as PointOptionsObject[]

  // Преобразование фактических данных для отображение scatter графика
  const scatterSeriesData = actualData.map(([x, y]) => ({
    x: Number(x),
    y: Number(y),
  }))

  const series: Highcharts.SeriesOptionsType[] = []

  if (lineSeriesData.length > 0) {
    series.push({
      type: 'line',
      name: 'Характеристика удельного расхода ГЭС',
      data: lineSeriesData,
      // Повышение порога до включения turbo mode со стандартного 1000
      // для решения проблемы невозможности отрисовки большого количества точек на графике,
      // если они передаются в виде объекта, а не массива
      turboThreshold: 25000,
    })
  }

  if (actualData.length > 0) {
    series.push({
      type: 'scatter',
      name: 'Фактические данные',
      data: scatterSeriesData,
      marker: {
        symbol: 'circle',
        radius: 5,
      },
    })
  }

  const options: Highcharts.Options = {
    chart: {
      height: 715,
    },
    title: {
      text: '',
    },
    xAxis: {
      title: {
        text: `Напор, м`,
      },
    },
    yAxis: {
      title: {
        text: `Удельный расход, ${unit}`,
      },
    },
    series,
    tooltip: {
      useHTML: true,
      // Формируем кастомный тултип в зависимости от типа графика:
      // - line - отображаем номер строки и координаты
      // - scatter - отображаем только координаты
      formatter: function () {
        const point = this.point as CustomPoint
        const rowNumberRow =
          this.series.type === 'line'
            ? `
          <tr>
            <th style="padding: 2px; font-weight: bold;">Строка:</th>
            <td>${point.rowNumber}</td>
          </tr>
        `
            : ''

        return `
          <table>
            ${rowNumberRow}
            <tr>
              <th style="padding: 2px; font-weight: bold;">Удельный расход:</th>
              <td>${this.y} ${unit}</td>
            </tr>
            <tr>
              <th style="padding: 2px; font-weight: bold;">Напор:</th>
              <td>${this.x} м</td>
            </tr>
          </table>
        `
      },
    },
    exporting: {
      buttons: {
        contextButton: {
          menuItems: ['viewFullscreen', 'separator', 'downloadPNG', 'downloadSVG'],
        },
      },
    },
  }

  // Передаём key, что бы происходил unmount компонента для правильного отображения графика
  // во время редактирования значений в таблице SpecificConsumptionSpreadsheet
  return <Chart options={options} key={JSON.stringify(specificConsumptionSpreadsheetData.data)} />
})
