.container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  height: 100%;
}

.actionContainer {
  display: flex;
  justify-content: space-between;
}

.settingsContainer {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mainSection {
  display: flex;
  gap: 20px;
}

.spreadsheetContainer {
  flex-shrink: 0;
  max-width: 55%;
  overflow: auto;
}

.chartContainer {
  flex: 1;
  min-width: 480px;
}
