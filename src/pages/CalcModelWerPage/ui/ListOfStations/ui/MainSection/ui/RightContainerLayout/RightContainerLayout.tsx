import { observer } from 'mobx-react'
import { Dispatch, FC, ReactNode, SetStateAction } from 'react'
import { Button } from 'shared/ui/Button'
import { Toggle } from 'shared/ui/Toggle'
import { IWerListOfStationsStore } from 'stores/CalcModelWerStore/WerListOfStationsStore/WerListOfStationsStore.types'
import { useStore } from 'stores/useStore.ts'

import cls from './RightContainerLayout.module.scss'

interface RightContainerLayoutProps {
  children: ReactNode
  onSave: () => Promise<void>
  onReset: () => void
  isModified: boolean
}

export const RightContainerLayout: FC<RightContainerLayoutProps> = observer((props) => {
  const { children, onSave, onReset, isModified } = props
  const {
    calcModelWerStore: {
      selectedPlant,
      listOfStationsStore: { tabs, activeTab, setActiveTabs },
    },
  } = useStore()

  return (
    <div className={cls.right}>
      <div className={cls.dataContainer}>
        <div className={cls.headerRight}>
          <div className={cls.headerTitle}>{selectedPlant?.label}</div>
          <div className={cls.toggleButtons}>
            <Toggle<IWerListOfStationsStore['tabs'][0]['value']>
              items={tabs}
              value={activeTab.value}
              setValue={setActiveTabs as Dispatch<SetStateAction<IWerListOfStationsStore['tabs'][0]['value']>>}
            />
          </div>
          <div className={cls.actions}>
            <Button disabled={!isModified} variant='outlined' onClick={onReset}>
              Сбросить
            </Button>
            <Button disabled={!isModified} onClick={onSave}>
              Сохранить
            </Button>
          </div>
        </div>
        {children}
      </div>
    </div>
  )
})
