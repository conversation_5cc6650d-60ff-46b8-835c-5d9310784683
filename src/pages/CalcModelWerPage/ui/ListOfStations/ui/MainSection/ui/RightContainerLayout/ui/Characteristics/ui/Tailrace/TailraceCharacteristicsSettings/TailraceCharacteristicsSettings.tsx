import { FormControlLabel, IconButton, Radio, RadioGroup } from '@mui/material'
import { ITailraceSettings } from 'entities/api/calcModelWerManager.entities'
import { DATE_FORMATS } from 'entities/constants'
import { observer } from 'mobx-react'
import { useEffect, useState } from 'react'
import { parseISO8601DateMonth } from 'shared/lib/dateFormates'
import { Button } from 'shared/ui/Button'
import { DatePicker } from 'shared/ui/DatePicker'
import { Icon } from 'shared/ui/Icon'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Modal } from 'shared/ui/Modal'
import { Row } from 'shared/ui/Row'
import { Switch } from 'shared/ui/Switch'
import { TextField } from 'shared/ui/TextField'
import { useStore } from 'stores/useStore'

import { getDeclinedPlantName } from './lib'
import cls from './TailraceCharacteristicsSettings.module.scss'
import { FreezingToggle } from './ui/FreezingToggle'
import { OffsettedTooltip } from './ui/OffsetedTooltip'

export const TailraceCharacteristicsSettings = observer(() => {
  const {
    calcModelWerStore: {
      formattedDate,
      isSelectedDateEditable,
      selectedPlant,
      isSelectedPlantViewOnly,
      listOfStationsStore: {
        characteristicsStore: {
          editMode,
          tailraceStore: {
            getTailraceCharacteristics,
            setDownstreamPlantLevel,
            addDownstreamPlantLevel,
            deleteDownstreamPlantLevel,
            saveTailraceSettings,
            currentSettings,
            isSettingsModified,
            isSettingsSaving,
            freezing,
            lastFilledRow,
            freezingLastFilledRow,
            isEditRows,
            resetSettings,
            setRowCount,
            setFreezingWatch,
            setFreezingDate,
            setTailraceAllowableChange,
            setConsumptionAllowableChange,
            setEmptyValuesValidation,
            setAllowableChangeValidation,
            setMonotonyValidation,
            setPolynom,
            setMethod,
          },
        },
      },
    },
  } = useStore()

  const [isOpenCharacteristicsSettingsModal, setIsOpenCharacteristicsSettingsModal] = useState(false)

  useEffect(() => {
    if (!selectedPlant) return

    getTailraceCharacteristics(selectedPlant.plantId, formattedDate, undefined, true)
  }, [selectedPlant, formattedDate])

  // Добавляем пустой уровень при открытии модального окна, если уровней изначально нет
  useEffect(() => {
    if (
      isOpenCharacteristicsSettingsModal &&
      currentSettings?.downstreamPlant &&
      currentSettings?.downstreamPlantLevels?.length === 0
    ) {
      addDownstreamPlantLevel()
    }
  }, [isOpenCharacteristicsSettingsModal, currentSettings])

  if (!currentSettings) return null

  const handleSaveCharacteristicsSettings = async () => {
    if (!selectedPlant || !currentSettings) return

    try {
      await saveTailraceSettings(selectedPlant.plantId, formattedDate, currentSettings)
      setIsOpenCharacteristicsSettingsModal(false)
    } catch (error) {
      console.error('Произошла ошибка при сохранении настроек НБ', error)
    }
  }

  const isValidLevel = (value: string | number) => Number(value) > 0.001 && !Number.isNaN(Number(value))

  const isAnyLevelInvalid = currentSettings.downstreamPlantLevels?.some((value) => !isValidLevel(value))
  const isAnyPolynomValueInvalid = currentSettings.polynom.some((value) => Number.isNaN(Number(value)))

  const isSaveButtonDisabled = !isSettingsModified || isAnyPolynomValueInvalid || isAnyLevelInvalid

  const isOpenCharacteristicsSettingsModalButtonDisabled =
    !editMode || !isSelectedDateEditable || isSelectedPlantViewOnly || isEditRows

  return (
    <div>
      <div className={cls.characteristicsContainer}>
        <Button
          variant='outlined'
          onClick={() => setIsOpenCharacteristicsSettingsModal(true)}
          disabled={isOpenCharacteristicsSettingsModalButtonDisabled}
        >
          Настройка характеристики
        </Button>
        <FreezingToggle />
      </div>

      <Modal
        open={isOpenCharacteristicsSettingsModal}
        maxWidth='xl'
        title='Настройка характеристики'
        onClose={() => {
          setIsOpenCharacteristicsSettingsModal(false)
          resetSettings()
        }}
        actions={
          <div className={cls.actionContainer}>
            <LoadingButton
              variant='contained'
              loading={isSettingsSaving}
              disabled={isSaveButtonDisabled}
              onClick={handleSaveCharacteristicsSettings}
              className={cls.saveButton}
            >
              Сохранить
            </LoadingButton>
          </div>
        }
      >
        <div className={cls.body}>
          <h2 className={cls.titleStation}>{selectedPlant!.name}</h2>
          <Row
            label={
              <div className={cls.rowWithInfo}>
                <p>Количество строк</p>
                <OffsettedTooltip
                  title={<span>Последняя заполненная строка: {freezing ? freezingLastFilledRow : lastFilledRow}</span>}
                  offset={[100, -65]}
                />
              </div>
            }
          >
            <TextField
              type='number'
              maxNumber={20000}
              numberOption={{ isInteger: true, lengthBeforeComma: 5 }}
              value={Number.isNaN(Number(currentSettings.rowCount)) ? '' : currentSettings.rowCount}
              onChange={(e) => setRowCount(e.target.value)}
              className={cls.rowCountTextField}
            />
          </Row>
          {currentSettings.downstreamPlant && (
            <Row
              label={
                <div className={cls.multiLineLabel}>
                  <div className={cls.rowWithInfo}>
                    <div>Уровни водохранилища</div>
                    <OffsettedTooltip title='Данные в шапке столбцов' offset={[85, -65]} />
                  </div>
                  <div className={cls.titleStation}>{getDeclinedPlantName(currentSettings.downstreamPlant.name)}</div>
                </div>
              }
            >
              <div className={cls.downstreamLevelsContainer}>
                <IconButton onClick={addDownstreamPlantLevel} className={cls.addLevelButton}>
                  <Icon name='plus' width={13} height={13} />
                </IconButton>

                <div className={cls.levelsScrollContainer}>
                  {(currentSettings.downstreamPlantLevels || []).map((value, index) => (
                    <div key={index} className={cls.levelRow}>
                      <TextField
                        type='number'
                        numberOption={{ lengthAfterComma: 3, positive: true }}
                        value={value ?? ''}
                        onChange={(e) => setDownstreamPlantLevel(index, e.target.value)}
                        className={cls.levelTextField}
                        error={!isValidLevel(value)}
                      />
                      {currentSettings.downstreamPlantLevels && currentSettings.downstreamPlantLevels.length > 1 && (
                        <IconButton onClick={() => deleteDownstreamPlantLevel(index)} className={cls.iconRedBtn}>
                          <Icon name='trash' width={13} height={13} />
                        </IconButton>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </Row>
          )}
          <Row label='Учет ледостава в НБ'>
            <div className={cls.freezingWatchContainer}>
              <div className={cls.freezingWatchSwitchContainer}>
                <Switch checked={currentSettings.freezingWatch} onChange={(_, checked) => setFreezingWatch(checked)} />
              </div>
              <DatePicker
                value={parseISO8601DateMonth(currentSettings.freezingBegin)}
                setValue={(value) => setFreezingDate('freezingBegin', value)}
                views={['day', 'month']}
                format={DATE_FORMATS.ddMM}
                disabled={!currentSettings.freezingWatch}
                className={cls.freezingWatchDatePicker}
              />
              <DatePicker
                value={parseISO8601DateMonth(currentSettings.freezingEnd)}
                setValue={(value) => setFreezingDate('freezingEnd', value)}
                views={['day', 'month']}
                format={DATE_FORMATS.ddMM}
                disabled={!currentSettings.freezingWatch}
                className={cls.freezingWatchDatePicker}
              />
            </div>
          </Row>
          <Row label='Валидация пустых значений'>
            <Switch
              checked={currentSettings.emptyValuesValidation ?? false}
              onChange={(_, checked) => setEmptyValuesValidation(checked)}
            />
          </Row>
          <Row label='Валидация изменения ближайших значений' contentClassName={cls.closedParameters}>
            <Switch
              checked={currentSettings.allowableChangeValidation ?? false}
              onChange={(_, checked) => setAllowableChangeValidation(checked)}
            />
            <TextField
              label='метры'
              type='number'
              numberOption={{ lengthAfterComma: 3 }}
              value={
                Number.isNaN(Number(currentSettings.tailraceAllowableChange))
                  ? ''
                  : currentSettings.tailraceAllowableChange
              }
              onChange={(e) => {
                setTailraceAllowableChange(e.target.value)
              }}
              disabled={!currentSettings.allowableChangeValidation}
              error={currentSettings.allowableChangeValidation && !currentSettings.tailraceAllowableChange}
            />
            <TextField
              label='м³/с'
              type='number'
              numberOption={{ lengthAfterComma: 3 }}
              value={
                Number.isNaN(Number(currentSettings.consumptionAllowableChange))
                  ? ''
                  : currentSettings.consumptionAllowableChange
              }
              onChange={(e) => setConsumptionAllowableChange(e.target.value)}
              disabled={!currentSettings.allowableChangeValidation}
              error={currentSettings.allowableChangeValidation && !currentSettings.consumptionAllowableChange}
            />
          </Row>
          <Row label='Валидация монотонности изменения значений'>
            <Switch
              checked={currentSettings.monotonyValidation ?? false}
              onChange={(_, checked) => setMonotonyValidation(checked)}
            />
          </Row>
          {currentSettings.polynom.map((constant, index) => (
            <Row
              key={index}
              label={
                index === 3 ? (
                  <div className={cls.rowWithInfo}>
                    <p>Полином</p>
                    <OffsettedTooltip
                      title={
                        <div>
                          C<sub>0</sub> + C<sub>1</sub> &bull; x + C<sub>2</sub> &bull; x<sup>2</sup> + C<sub>3</sub>{' '}
                          &bull; x<sup>3</sup> + C<sub>4</sub> &bull; x<sup>4</sup> + C<sub>5</sub> &bull; x<sup>5</sup>{' '}
                          + C<sub>6</sub> &bull; x<sup>6</sup>
                        </div>
                      }
                      offset={[160, -70]}
                    />
                  </div>
                ) : null
              }
            >
              <TextField
                label={`C${index}`}
                type='number'
                numberOption={{ allowExponent: true, lengthAfterComma: 20 }}
                value={constant ?? ''}
                onChange={(e) => setPolynom(index, e.target.value)}
                error={Number.isNaN(Number(constant))}
                className={cls.polynomTextField}
              />
            </Row>
          ))}
          <Row label='Данные, используемые в расчётах'>
            <RadioGroup
              row
              value={currentSettings.method ?? 'TABLE'}
              onChange={(e) => setMethod(e.target.value as ITailraceSettings['method'])}
            >
              <FormControlLabel className={cls.radioButton} value='TABLE' control={<Radio />} label='Таблица' />
              <FormControlLabel className={cls.radioButton} value='POLYNOM' control={<Radio />} label='Полином' />
            </RadioGroup>
          </Row>
        </div>
      </Modal>
    </div>
  )
})
