import { observer } from 'mobx-react'
import { useEffect } from 'react'
import { useStore } from 'stores/useStore'

import cls from './SpecificConsumption.module.scss'
import { SpecificConsumptionActualMeasurementsLoader } from './SpecificConsumptionActualMeasurementsLoader'
import { SpecificConsumptionChart } from './SpecificConsumptionChart'
import { SpecificConsumptionSettings } from './SpecificConsumptionSettings'
import { SpecificConsumptionSpreadsheet } from './SpecificConsumptionSpreadsheet'

export const SpecificConsumption = observer(() => {
  const {
    calcModelWerStore: {
      listOfStationsStore: {
        characteristicsStore: {
          specificConsumptionStore: { resetStore },
        },
      },
    },
  } = useStore()

  useEffect(() => {
    return () => resetStore()
  }, [])

  return (
    <div className={cls.container}>
      <div className={cls.actionContainer}>
        <SpecificConsumptionSettings />
        <SpecificConsumptionActualMeasurementsLoader />
      </div>
      <div className={cls.mainSection}>
        <SpecificConsumptionSpreadsheet />
        <div className={cls.chartContainer}>
          <SpecificConsumptionChart />
        </div>
      </div>
    </div>
  )
})
