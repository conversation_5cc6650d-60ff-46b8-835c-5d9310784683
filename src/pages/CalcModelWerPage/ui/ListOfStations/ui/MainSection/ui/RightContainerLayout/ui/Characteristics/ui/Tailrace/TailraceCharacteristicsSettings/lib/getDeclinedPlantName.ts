export const getDeclinedPlantName = (plantName: string) => {
  const plantNameParts = plantName.split(' ')
  const gesIndex = plantNameParts.findIndex((part) => part === 'ГЭС')

  // Если "ГЭС" не найдено или оно первое в названии, возвращаем исходную строку
  if (gesIndex === -1 || gesIndex === 0) {
    return plantName
  }

  const adjectiveIndex = gesIndex - 1
  const adjective = plantNameParts[adjectiveIndex]
  let declinedAdjective: string | null = null

  // Определяем окончание и меняем на соответствующее в родительном падеже
  if (adjective.endsWith('ая')) {
    declinedAdjective = adjective.slice(0, -2) + 'ой'
  } else if (adjective.endsWith('яя')) {
    declinedAdjective = adjective.slice(0, -2) + 'ей'
  } else if (adjective.endsWith('ые')) {
    declinedAdjective = adjective.slice(0, -2) + 'ых'
  } else if (adjective.endsWith('ие')) {
    declinedAdjective = adjective.slice(0, -2) + 'их'
  }

  // Если замена невозможна, возвращаем исходное название
  if (!declinedAdjective) {
    return plantName
  }

  // Собираем новое название
  const newParts = [...plantNameParts]
  newParts[adjectiveIndex] = declinedAdjective

  return newParts.join(' ')
}
