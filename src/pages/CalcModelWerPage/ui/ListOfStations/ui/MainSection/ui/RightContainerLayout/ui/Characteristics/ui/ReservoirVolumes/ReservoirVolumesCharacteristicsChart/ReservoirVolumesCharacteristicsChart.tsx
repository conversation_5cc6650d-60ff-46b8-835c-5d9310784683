import Highcharts, { Point, PointOptionsObject } from 'highcharts'
import { observer } from 'mobx-react'
import { Chart } from 'shared/ui/Chart'
import { useStore } from 'stores/useStore'

import { getFormattedUnit } from '../../../lib'
import { generateChartData } from './lib'

interface CustomPoint extends Point {
  rowNumber?: number
}

export const ReservoirVolumesCharacteristicsChart = observer(() => {
  const { calcModelWerStore } = useStore()
  const { listOfStationsStore } = calcModelWerStore
  const { characteristicsStore } = listOfStationsStore
  const { reservoirVolumeStore } = characteristicsStore
  const { characteristicsDataSpreadsheet, currentSettings } = reservoirVolumeStore

  if (!characteristicsDataSpreadsheet || !currentSettings) {
    return null
  }

  const unit = getFormattedUnit(currentSettings.unit)
  // Приводим к типу PointOptionsObject, так как Highcharts по умолчанию не ожидает увидеть null значения для точек
  const chartData = generateChartData(characteristicsDataSpreadsheet.data) as PointOptionsObject[]

  const series: Highcharts.SeriesOptionsType[] = []

  if (chartData.length > 0) {
    series.push({
      type: 'line',
      name: 'Характеристика объемов водохранилища',
      data: chartData,
      // Повышение порога до включения turbo mode со стандартного 1000
      // для решения проблемы невозможности отрисовки большого количества точек на графике,
      // если они передаются в виде объекта, а не массива
      turboThreshold: 25000,
    })
  }
  const options: Highcharts.Options = {
    chart: {
      height: 680,
    },
    title: {
      text: '',
    },
    xAxis: {
      title: {
        text: `Объем водохранилища, ${unit}`,
      },
    },
    yAxis: {
      title: {
        text: 'Уровень водохранилища, м',
      },
    },
    series,
    tooltip: {
      useHTML: true,
      formatter: function () {
        const point = this.point as CustomPoint
        const rowNumber = point.rowNumber

        return `
          <table>
            <tr>
              <th style="padding: 2px; font-weight: bold;">Строка:</th>
              <td>${rowNumber}</td>
            </tr>
            <tr>
              <th style="padding: 2px; font-weight: bold;">Уровень:</th>
              <td>${this.y} м</td>
            </tr>
            <tr>
              <th style="padding: 2px; font-weight: bold;">Объем:</th>
              <td>${this.x} ${unit}</td>
            </tr>
          </table>
        `
      },
    },
    exporting: {
      buttons: {
        contextButton: {
          menuItems: ['viewFullscreen', 'separator', 'downloadPNG', 'downloadSVG'],
        },
      },
    },
  }

  // Передаём key, что бы происходил unmount компонента для правильного отображения графика
  // во время редактирования значений в таблице ReservoirVolumesCharacteristicsSpreadsheet
  return <Chart options={options} key={JSON.stringify(characteristicsDataSpreadsheet.data)} />
})
