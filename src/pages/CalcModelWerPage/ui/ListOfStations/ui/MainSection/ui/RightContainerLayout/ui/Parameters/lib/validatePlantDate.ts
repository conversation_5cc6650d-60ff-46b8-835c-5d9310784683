import { isAfter, isBefore, isValid } from 'date-fns'

export const validatePlantStartDate = (startDate: Date | null, endDate: Date | null) => {
  if (startDate && !isValid(startDate)) {
    return 'Дата начала должна быть заполнена'
  } else if (startDate && endDate && isValid(startDate) && isValid(endDate) && isAfter(startDate, endDate)) {
    return 'Дата начала должна быть раньше даты окончания'
  }

  return ''
}

export const validatePlantEndDate = (endDate: Date | null, startDate: Date | null) => {
  if (endDate && startDate && isValid(endDate) && isValid(startDate) && isBefore(endDate, startDate)) {
    return 'Дата окончания должна быть позже даты начала'
  }

  return ''
}
