import Handsontable from 'handsontable'
import { observer } from 'mobx-react'
import { formatSpecificConsumptionNumericValue } from 'stores/CalcModelWerStore/WerListOfStationsStore/WerCharacteristicsStore/WerSpecificConsumptionStore/lib'
import { useStore } from 'stores/useStore'
import { handlePastSpreadsheetCellValue } from 'widgets/Spreadsheet/ui/lib'
import { SpreadsheetReact } from 'widgets/SpreadsheetReact'
import { SpreadsheetReactProps } from 'widgets/SpreadsheetReact/SpreadsheetReact'

import cls from './SpecificConsumptionSpreadsheet.module.scss'

type GridSettings = Handsontable.GridSettings

export const SpecificConsumptionSpreadsheet = observer(() => {
  const { calcModelWerStore } = useStore()
  const { listOfStationsStore } = calcModelWerStore
  const { characteristicsStore } = listOfStationsStore
  const { specificConsumptionStore } = characteristicsStore
  const { specificConsumptionSpreadsheetData, updateCharacteristicsSpreadsheetData, isCharacteristicsLoaded } =
    specificConsumptionStore
  const { data, cell, nestedHeaders, columns, rowHeaders } = specificConsumptionSpreadsheetData

  const colHeaders = nestedHeaders.flatMap((row) => row.map((header) => header.label))

  /**
   * Хук beforePaste
   *
   * Обрабатывает вставку (CTRL+V) данных в таблицу.
   * Для каждой ячейки, в зависимости от позиции (номер столбца), форматирует число с нужным
   * количеством знаков после запятой (в первой колонке – 1, в остальных – 3).
   *
   * @param data - двумерный массив вставляемых данных.
   * @param coords - массив объектов с координатами вставки (начало вставки, диапазон и т.д.).
   */
  const beforePaste: Handsontable.GridSettings['beforePaste'] = (data, coords) => {
    if (coords && coords.length > 0) {
      // Предположим, вставка начинается с определённого столбца
      const startCol = coords[0].startCol
      const processedData = data
        .map((row) =>
          row
            .map((cellValue, colIndex) =>
              handlePastSpreadsheetCellValue(cellValue, (cellValue) => {
                // Вычисляем, в какой столбец попадёт ячейка
                const targetColIndex = startCol + colIndex
                // Для первой колонки задаем maxDecimals = 1, для другой = 3
                const maxDecimals = targetColIndex === 0 ? 1 : 3

                return formatSpecificConsumptionNumericValue(cellValue, true, maxDecimals)
              }),
            )
            .filter((cellValue) => cellValue !== undefined),
        )
        .filter((row) => row.length > 0)

      if (processedData.length) {
        // Очищаем оригинальный массив и вставляем обработанные данные
        data.length = 0
        data.push(...processedData)
      } else {
        return false
      }
    }
  }

  /**
   * Хук beforeAutofill
   *
   * Вызывается при автозаполнении (перетаскивании квадратика заполнения) диапазона данных.
   * Обрабатывает каждое значение аналогично beforePaste – в зависимости от позиции в целевом
   * диапазоне форматирует число с нужным количеством знаков после запятой.
   *
   * @param selectionData - двумерный массив исходных данных для автозаполнения.
   * @param _sourceRange - исходный диапазон (не используется).
   * @param targetRange - целевой диапазон, в который будут вставлены данные.
   *
   * @returns обработанный массив данных для автозаполнения.
   */
  const beforeAutofill: Handsontable.GridSettings['beforeAutofill'] = (selectionData, _sourceRange, targetRange) => {
    // Получаем индекс первого столбца диапазона автозаполнения
    const startCol = targetRange.from.col
    const processedData = selectionData.map((row) =>
      row.map((cellValue, colIndex) => {
        const targetColIndex = startCol + colIndex
        // Для первой колонки задаём maxDecimals = 1, для другой = 3
        const maxDecimals = targetColIndex === 0 ? 1 : 3

        return formatSpecificConsumptionNumericValue(cellValue, true, maxDecimals)
      }),
    )
    // Перезаписываем данные автозаполнения обработанными значениями
    selectionData.length = 0
    selectionData.push(...processedData)

    return selectionData
  }

  /**
   * Хук afterGetColHeader
   *
   * Функция-обработчик, стилизующая заголовки столбцов.
   * Если индекс столбца равен -1, используется для отображения нумерации строк.
   * Также выполняется разбивка заголовка на две строки, если он содержит запятую.
   *
   * @param col - индекс столбца (если равен -1, это нумерация строк).
   * @param TH - HTML-элемент, в который выводится заголовок столбца.
   */
  const setColumnHeader: GridSettings['afterGetColHeader'] = (col, TH) => {
    const isNumberColumn = col === -1
    let headerText = isNumberColumn ? '№' : (TH.textContent ?? '')

    // Если не нумерная колонка и текст содержит запятую, разбиваем его на две строки.
    if (!isNumberColumn && headerText.includes(',')) {
      headerText = headerText.replace(/,\s*/, ',<br/>')
    }

    // Сбрасываем стили перед добавлением новых
    TH.innerHTML = ''

    const div = document.createElement('div')
    div.style.cssText = `
      height: 60px;
      line-height: normal;
      white-space: normal;
      word-wrap: break-word;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
    `

    const hasError = cell.some((cellItem) => Boolean(cellItem.comment))
    if (hasError) {
      TH.classList.add('isNotValid')
    }

    div.innerHTML = headerText
    TH.appendChild(div)

    TH.classList.add(cls.bold)
  }

  if (!isCharacteristicsLoaded) return null

  const tableSettings: Partial<SpreadsheetReactProps> = {
    data,
    columns,
    rowHeaders,
    colHeaders,
    cell,
    height: 720,
    width: 273,
    rowHeaderWidth: 45,
    colWidths: [96, 120],
    maxRows: data.length,
    afterChange: updateCharacteristicsSpreadsheetData,
    beforePaste,
    beforeAutofill,
    afterGetColHeader: setColumnHeader,
  }

  return <SpreadsheetReact {...tableSettings} />
})
