import Handsontable from 'handsontable'
import { observer } from 'mobx-react'
import { formatSpecificConsumptionNumericValue } from 'stores/CalcModelWerStore/WerListOfStationsStore/WerCharacteristicsStore/WerSpecificConsumptionStore/lib'
import { useStore } from 'stores/useStore'
import { handlePastSpreadsheetCellValue } from 'widgets/Spreadsheet/ui/lib'
import { SpreadsheetReact } from 'widgets/SpreadsheetReact'
import { SpreadsheetReactProps } from 'widgets/SpreadsheetReact/SpreadsheetReact'

import cls from './TailraceCharacteristicsSpreadsheet.module.scss'

type GridSettings = Handsontable.GridSettings

/**
 * Хук beforePaste
 *
 * Обрабатывает вставку (CTRL+V) данных в таблицу.
 * Для каждой ячейки, в зависимости от позиции (номер столбца), форматирует число с нужным
 * количеством знаков после запятой (в первой колонке – 1, в остальных – 3).
 *
 * @param data - двумерный массив вставляемых данных.
 * @param coords - массив объектов с координатами вставки (начало вставки, диапазон и т.д.).
 */
const beforePaste: Handsontable.GridSettings['beforePaste'] = (data, coords) => {
  if (coords && coords.length > 0) {
    // Предположим, вставка начинается с определённого столбца
    const startCol = coords[0].startCol
    const processedData = data
      .map((row) =>
        row
          .map((cellValue, colIndex) =>
            handlePastSpreadsheetCellValue(cellValue, (cellValue) => {
              // Вычисляем, в какой столбец попадёт ячейка
              const targetColIndex = startCol + colIndex
              // Для первой колонки задаем maxDecimals = 1, для остальных = 3
              const maxDecimals = targetColIndex === 0 ? 1 : 3

              return formatSpecificConsumptionNumericValue(cellValue, true, maxDecimals)
            }),
          )
          .filter((cellValue) => cellValue !== undefined),
      )
      .filter((row) => row.length > 0)

    if (processedData.length) {
      // Очищаем оригинальный массив и вставляем обработанные данные
      data.length = 0
      data.push(...processedData)
    } else {
      return false
    }
  }
}

/**
 * Хук beforeAutofill
 *
 * Вызывается при автозаполнении (перетаскивании квадратика заполнения) диапазона данных.
 * Обрабатывает каждое значение аналогично beforePaste – в зависимости от позиции в целевом
 * диапазоне форматирует число с нужным количеством знаков после запятой.
 *
 * @param selectionData - двумерный массив исходных данных для автозаполнения.
 * @param _sourceRange - исходный диапазон (не используется).
 * @param targetRange - целевой диапазон, в который будут вставлены данные.
 *
 * @returns обработанный массив данных для автозаполнения.
 */
const beforeAutofill: Handsontable.GridSettings['beforeAutofill'] = (selectionData, _sourceRange, targetRange) => {
  // Получаем индекс первого столбца диапазона автозаполнения
  const startCol = targetRange.from.col
  const processedData = selectionData.map((row) =>
    row.map((cellValue, colIndex) => {
      const targetColIndex = startCol + colIndex
      // Для первой колонки задаём maxDecimals = 1, для остальных = 3
      const maxDecimals = targetColIndex === 0 ? 1 : 3

      return formatSpecificConsumptionNumericValue(cellValue, true, maxDecimals)
    }),
  )
  // Перезаписываем данные автозаполнения обработанными значениями
  selectionData.length = 0
  selectionData.push(...processedData)

  return selectionData
}

/**
 * Устанавливает ширину контейнера таблицы в соответствии с её реальной шириной на странице.
 *
 * Функция получает экземпляр таблицы Handsontable и корректирует ширину контейнера с id="spreadsheetContainer" так,
 * чтобы она соответствовала ширине внутреннего контейнера `.wtHider` с небольшим
 * дополнительным отступом. Этот отступ (`SCROLLBAR_MARGIN`) необходим для того,
 * чтобы вертикальный скроллбар не прилипал вплотную к таблице, а оставался небольшой
 * визуальный зазор, а также не появлялся горизонтальный скролл.
 *
 * @param hot - экземпляр Handsontable, для которого требуется обновить ширину.
 */
const updateTableWidthContainer = (hot: Handsontable) => {
  if (!hot) return

  const SCROLLBAR_MARGIN = 12

  const wtHider = hot.rootElement.querySelector('.wtHider')
  const spreadsheetContainer = document.getElementById('spreadsheetContainer')

  if (spreadsheetContainer && wtHider) {
    spreadsheetContainer.style.width = `${wtHider.clientWidth + SCROLLBAR_MARGIN}px`
  }
}

export const TailraceCharacteristicsSpreadsheet = observer(() => {
  const { calcModelWerStore } = useStore()
  const { listOfStationsStore } = calcModelWerStore
  const { characteristicsStore } = listOfStationsStore
  const { tailraceStore } = characteristicsStore
  const {
    tailraceSpreadsheetData,
    freezingTailraceSpreadsheetData,
    isCharacteristicsLoaded,
    updateCharacteristicsSpreadsheetData,
    initialSettings,
    downstreamPlantList,
    freezing,
  } = tailraceStore

  // Выбираем текущий набор данных в зависимости от типа таблицы (зимняя/летняя)
  const currentSpreadsheetData = freezing ? freezingTailraceSpreadsheetData : tailraceSpreadsheetData
  const { data, cell, nestedHeaders, columns, rowHeaders } = currentSpreadsheetData

  const colHeaders = nestedHeaders.flatMap((row) => row.map((header) => header.label))

  const hasFreezingWatch = !!initialSettings?.freezingWatch
  const hasDownstream = downstreamPlantList.length > 0

  // Общие настройки для всех таблиц
  const baseTableSettings: Partial<SpreadsheetReactProps> = {
    data,
    columns,
    rowHeaders,
    cell,
    afterChange: updateCharacteristicsSpreadsheetData,
    beforePaste,
    beforeAutofill,
    height: 720,
    rowHeaderWidth: 45,
    maxRows: data.length,
    afterInit: function (this: Handsontable) {
      updateTableWidthContainer(this)
    },
  }

  const getTableConfiguration = (): Partial<SpreadsheetReactProps> => {
    /**
     * Хук afterGetColHeader
     *
     * Функция-обработчик, стилизующая заголовки столбцов.
     * Если индекс столбца равен -1, используется для отображения нумерации строк.
     * Также выполняется разбивка заголовка на две строки, если он содержит запятую.
     *
     * @param col - индекс столбца (если равен -1, это нумерация строк).
     * @param TH - HTML-элемент, в который выводится заголовок столбца.
     */
    const defaultAfterGetColHeader: GridSettings['afterGetColHeader'] = (col, TH) => {
      const isNumberColumn = col === -1
      let headerText = isNumberColumn ? '№' : (TH.textContent ?? '')

      // Если не нумерная колонка и текст содержит запятую, разбиваем его на две строки.
      if (!isNumberColumn && headerText.includes(',')) {
        headerText = headerText.replace(/,\s*/, ',<br/>')
      }

      // Сбрасываем стили перед добавлением новых
      TH.innerHTML = ''

      const div = document.createElement('div')
      div.style.cssText = `
        height: 60px;
        line-height: normal;
        white-space: normal;
        word-wrap: break-word;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        font-weight: 700;
      `

      const hasError = cell.some((cellItem) => Boolean(cellItem.comment))

      if (hasError) {
        TH.classList.add('isNotValid')
      }

      div.innerHTML = headerText
      TH.appendChild(div)
    }

    const downstreamAfterGetColHeader: GridSettings['afterGetColHeader'] = (col, TH, headerLevel) => {
      // Проверяем, есть ли ошибка хоть в одной ячейке
      const hasError = cell.some((cellItem) => Boolean(cellItem.comment))
      if (hasError) {
        TH.classList.add('isNotValid')
      }

      if (col === -1 && headerLevel === 2) {
        TH.innerHTML = ''
        const div = document.createElement('div')
        div.style.cssText = `
          height: 17px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 700;
        `
        div.innerHTML = '№'
        TH.appendChild(div)
      } else if (col !== -1) {
        TH.classList.add(cls.bold)
      }
    }

    if (hasDownstream) {
      return {
        nestedHeaders,
        colWidths: 60,
        afterGetColHeader: downstreamAfterGetColHeader,
        height: 685,
      }
    }

    if (hasFreezingWatch) {
      return {
        colHeaders,
        width: 423,
        colWidths: [96, 120, 150],
        afterGetColHeader: defaultAfterGetColHeader,
      }
    }

    return {
      colHeaders,
      width: 273,
      colWidths: [96, 120],
      afterGetColHeader: defaultAfterGetColHeader,
    }
  }

  if (!isCharacteristicsLoaded) return null

  return <SpreadsheetReact {...baseTableSettings} {...getTableConfiguration()} />
})
