.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 30px;
  width: 100%;
  overflow: auto;

  --title-width: 200px;
}

.noData {
  background-color: var(--background-color-secondary);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  color: var(--text-gray);
}

.loaderBlock {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
