import Highcharts, { Point, PointOptionsObject } from 'highcharts'
import { observer } from 'mobx-react'
import { Chart } from 'shared/ui/Chart'
import { useStore } from 'stores/useStore'

import { mapSpreadsheetDataToPoints, mapSpreadsheetDataToPointsForDownstream } from './lib'

interface CustomPoint extends Point {
  rowNumber?: number
}

export const TailraceCharacteristicsChart = observer(() => {
  const { calcModelWerStore } = useStore()
  const { listOfStationsStore } = calcModelWerStore
  const { characteristicsStore } = listOfStationsStore
  const { tailraceStore } = characteristicsStore
  const {
    isCharacteristicsLoaded,
    currentSettings,
    tailraceSpreadsheetData,
    freezingTailraceSpreadsheetData,
    downstreamPlantList,
    seriesActualData,
  } = tailraceStore

  if (!isCharacteristicsLoaded || !currentSettings) {
    return null
  }

  // Определяем текущую конфигурацию графика
  const hasFreezingWatch = !!currentSettings?.freezingWatch
  const hasDownstream = downstreamPlantList.length > 0

  // Формируем серии для графика
  const series: Highcharts.SeriesOptionsType[] = []

  if (!hasDownstream) {
    // Обработка случая без нижележащих ГЭС с включенным подпором
    // Получаем данные для серий с учетом вида таблицы
    const seriesData = mapSpreadsheetDataToPoints(tailraceSpreadsheetData.data, hasFreezingWatch)

    // Добавляем основную серию (таблица из 2х столбцов)
    if (seriesData[0] && seriesData[0].length > 0) {
      series.push({
        type: 'line',
        name: 'Уровень НБ',
        data: seriesData[0] as PointOptionsObject[],
        turboThreshold: 25000,
      })
    }

    // Добавляем серию с учетом ледостава, если она есть (таблица из 3х столбцов)
    if (hasFreezingWatch && seriesData[1] && seriesData[1].length > 0) {
      series.push({
        type: 'line',
        name: 'Уровень НБ с учетом ледостава',
        data: seriesData[1] as PointOptionsObject[],
        dashStyle: 'Dash',
        turboThreshold: 25000,
      })
    }
  } else {
    // Обработка случая с нижележащими ГЭС с включенным подпором
    if (!currentSettings.downstreamPlantLevels || currentSettings.downstreamPlantLevels.length === 0) {
      return null
    }

    const normalSeriesData = mapSpreadsheetDataToPointsForDownstream(
      tailraceSpreadsheetData.data,
      currentSettings?.downstreamPlantLevels,
    )

    // Добавляем серию с выключенным "Учетом ледостава"
    normalSeriesData.forEach((seriesPoints, index) => {
      if (seriesPoints && seriesPoints.length > 0) {
        const level = currentSettings?.downstreamPlantLevels?.[index]
        series.push({
          type: 'line',
          name: `Уровень НБ (${level})`,
          data: seriesPoints as PointOptionsObject[],
          turboThreshold: 25000,
        })
      }
    })

    // Если включен "Учет ледостава", добавляем соответствующие пунктирные линии
    if (hasFreezingWatch) {
      const freezingSeriesData = mapSpreadsheetDataToPointsForDownstream(
        freezingTailraceSpreadsheetData.data,
        currentSettings?.downstreamPlantLevels,
      )

      freezingSeriesData.forEach((seriesPoints, index) => {
        if (seriesPoints && seriesPoints.length > 0) {
          const level = currentSettings?.downstreamPlantLevels?.[index]
          series.push({
            type: 'line',
            name: `Уровень НБ (с учетом ледостава) (${level})`,
            data: seriesPoints as PointOptionsObject[],
            dashStyle: 'Dash',
            turboThreshold: 25000,
          })
        }
      })
    }
  }

  // Добавляем серию фактических данных после загрузки
  if (seriesActualData.length > 0) {
    series.push({
      type: 'scatter',
      name: 'Фактические данные',
      data: seriesActualData,
      marker: {
        symbol: 'circle',
        radius: 5,
      },
    })
  }

  const options: Highcharts.Options = {
    chart: {
      height: 680,
    },
    title: {
      text: '',
    },
    xAxis: {
      title: {
        text: `Расход воды, м³/с`,
      },
    },
    yAxis: {
      title: {
        text: `Уровень нижнего бьефа, м`,
      },
    },
    series,
    tooltip: {
      useHTML: true,
      // Формируем кастомный тултип в зависимости от типа графика:
      // - line - отображаем номер строки и координаты
      // - scatter - отображаем только координаты
      formatter: function () {
        const point = this.point as CustomPoint
        const yCoordName = this.series.type === 'scatter' ? 'Уровень НБ' : this.series.name
        const rowNumberRow =
          this.series.type === 'line'
            ? `
          <tr>
            <th style="padding: 2px; font-weight: bold;">Строка:</th>
            <td>${point.rowNumber}</td>
          </tr>
        `
            : ''

        return `
          <table>
            ${rowNumberRow}
            <tr>
              <th style="padding: 2px; font-weight: bold;">${yCoordName}:</th>
              <td>${this.y} м</td>
            </tr>
            <tr>
              <th style="padding: 2px; font-weight: bold;">Расход воды:</th>
              <td>${this.x} м³/с</td>
            </tr>
          </table>
        `
      },
    },
    exporting: {
      buttons: {
        contextButton: {
          menuItems: ['viewFullscreen', 'separator', 'downloadPNG', 'downloadSVG'],
        },
      },
    },
  }

  // Передаём key, что бы происходил unmount компонента для правильного отображения графика
  // во время редактирования значений в таблице TailraceCharacteristicsSpreadsheet
  return <Chart options={options} key={JSON.stringify(tailraceSpreadsheetData.data)} />
})
