import CloseIcon from '@mui/icons-material/Close'
import { Autocomplete, Chip, TextField } from '@mui/material'
import { observer } from 'mobx-react'
import { FC, useEffect, useState } from 'react'
import { useStore } from 'stores/useStore'

import cls from './Cascade.module.scss'

interface ICascade {
  id: number
  name: string
  archived: boolean
}

interface Props {
  isEditingEnabled: boolean
}

export const Cascade: FC<Props> = observer(({ isEditingEnabled }) => {
  const { calcModelWerStore } = useStore()
  const {
    listOfStationsStore: {
      parametersStore: { availableCascades, plantCascades, updateCascades },
    },
  } = calcModelWerStore

  const [focused, setFocused] = useState(false)
  const [selectedCascades, setSelectedCascades] = useState<ICascade[]>([])
  const [filteredAvailableCascades, setFilteredAvailableCascades] = useState<ICascade[]>([])

  const updateFilteredAvailableCascades = () => {
    const filtered = availableCascades.filter(
      (available) => !selectedCascades.some((selected) => selected.id === available.id) && !available.archived,
    )
    setFilteredAvailableCascades(filtered)
  }

  useEffect(() => {
    if (plantCascades?.cascades) {
      setSelectedCascades(plantCascades.cascades)
    }
  }, [plantCascades])

  useEffect(() => {
    updateFilteredAvailableCascades()
  }, [availableCascades, selectedCascades])

  const handleAddCascade = (_: React.SyntheticEvent<Element, Event>, newCascades: ICascade[]) => {
    const newSelected = newCascades.filter(
      (cascade) => !selectedCascades.some((selected) => selected.id === cascade.id),
    )

    const updatedSelectedCascades = [...selectedCascades, ...newSelected]
    setSelectedCascades(updatedSelectedCascades)

    updateCascades(updatedSelectedCascades)
  }

  const handleRemoveCascade = (cascadeToRemove: ICascade) => {
    const updatedSelectedCascades = selectedCascades.filter((cascade) => cascade.id !== cascadeToRemove.id)
    setSelectedCascades(updatedSelectedCascades)

    updateCascades(updatedSelectedCascades)
    // Пересчитываем `filteredAvailableCascades`
    updateFilteredAvailableCascades()
  }

  const handleClearAll = () => {
    setSelectedCascades([])
    updateCascades([])
    updateFilteredAvailableCascades()
  }

  return (
    <div className={cls.container}>
      <div className={cls.title}>Каскад</div>
      <Autocomplete
        disabled={!isEditingEnabled}
        className={cls.autocompleteContainer}
        size='small'
        multiple
        options={filteredAvailableCascades}
        getOptionLabel={(option) => option.name}
        value={selectedCascades}
        disableCloseOnSelect
        onChange={handleAddCascade}
        componentsProps={{
          popper: {
            style: { width: 'fit-content' },
          },
        }}
        noOptionsText='Нет доступных каскадов'
        openText='Открыть список каскадов'
        clearText='Очистить'
        renderTags={(cascades, getTagProps) =>
          cascades.map((cascade, index) => (
            <Chip
              {...getTagProps({ index })}
              key={cascade.id}
              label={cascade.name}
              onDelete={() => handleRemoveCascade(cascade)}
              deleteIcon={<CloseIcon className={cls.deleteIcon} fontSize='small' />}
              sx={{ '& .MuiChip-deleteIcon': { display: focused ? 'block' : 'none' } }}
              size='small'
            />
          ))
        }
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        renderInput={(params) => (
          <TextField
            {...params}
            sx={{
              '& .MuiInputBase-root': {
                minHeight: '40px',
              },
            }}
          />
        )}
        clearIcon={
          <CloseIcon
            className={cls.deleteIcon}
            fontSize='small'
            onClick={(e) => {
              e.stopPropagation()
              handleClearAll()
            }}
          />
        }
      />
    </div>
  )
})
