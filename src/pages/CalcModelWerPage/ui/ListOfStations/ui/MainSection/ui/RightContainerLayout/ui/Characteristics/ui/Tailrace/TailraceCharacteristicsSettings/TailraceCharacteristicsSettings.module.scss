.characteristicsContainer {
  display: flex;
  gap: 20px;
}

.body {
  width: 560px;
}

.titleStation {
  color: var(--text-gray);
}

.freezingWatchContainer {
  display: flex;
  gap: 10px;
}

.freezingWatchSwitchContainer {
  width: 95px;
}

.freezingWatchDatePicker {
  width: 95px;
}

.closedParameters {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;

  & > div {
    width: 95px;
  }
}

.actionContainer {
  display: flex;
  justify-content: flex-end;
}

.saveButton {
  width: 85px;
  height: 26px;
  margin-left: auto;
}

.rowWithInfo {
  display: flex;
  gap: 5px;
}

.polynomTextField {
  width: 200px;
}

.rowCountTextField {
  width: 200px;
}

.multiLineLabel {
  display: flex;
  flex-direction: column;
}

.downstreamLevelsContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  gap: 8px;
}

.levelsScrollContainer {
  max-height: 280px;
  width: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-right: 8px;
}

.levelRow {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.levelTextField {
  width: 95px;
}

.iconRedBtn {
  color: var(--red-color);
}

.addLevelButton {
  color: var(--primary-color);
}

.radioButton {
  margin-right: 5px;
}
