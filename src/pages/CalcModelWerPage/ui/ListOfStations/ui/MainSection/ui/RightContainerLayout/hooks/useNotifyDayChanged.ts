import { endOfDay } from 'date-fns'
import { useEffect, useRef, useState } from 'react'

export const useNotifyDayChanged = () => {
  const [isDayChanged, setIsDayChanged] = useState(false)
  const timeout = useRef<ReturnType<typeof setTimeout> | null>(null)

  useEffect(() => {
    /**
     * Обработка кейса с изменением текущей даты.
     * Если пользователь находится на завтрашней дате и начинается новый день - кнопки редактирования будут заблокированы.
     */
    const setEndDayTimeout = () => {
      /**
       * Количество миллисекунд, которые нужно прибавить к разнице между концом для и текущим временем
       * чтобы получилось нужное время до конца дня
       */
      const MILLISECONDS_TO_END_DAY = 1000
      const currentDate = new Date()
      const diff = endOfDay(currentDate).getTime() + MILLISECONDS_TO_END_DAY - currentDate.getTime()

      timeout.current = setTimeout(() => {
        setIsDayChanged((prev) => !prev)
        setEndDayTimeout()
      }, diff)
    }

    setEndDayTimeout()

    return () => {
      timeout.current && clearTimeout(timeout.current)
    }
  }, [])

  return isDayChanged
}
