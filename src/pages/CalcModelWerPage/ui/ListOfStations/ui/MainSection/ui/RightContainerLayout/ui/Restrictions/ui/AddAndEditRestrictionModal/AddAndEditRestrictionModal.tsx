import { DATE_FORMATS } from 'entities/constants'
import { observer } from 'mobx-react'
import {
  getInitialValues,
  getUnitsMeasurementValue,
  toInitialValue,
  validation,
} from 'pages/CalcModelWerPage/ui/ListOfStations/ui/MainSection/ui/RightContainerLayout/ui/Restrictions/ui/AddAndEditRestrictionModal/lib'
import { ItemValue, Values } from 'pages/ReportsPage/ui/ModalCreateEditReport/config/types.ts'
import { FC, useState } from 'react'
import { formatStr } from 'shared/lib/formatStr'
import { Button } from 'shared/ui/Button'
import { DatePicker } from 'shared/ui/DatePicker'
import { ErrorExplanationIcon } from 'shared/ui/ErrorExplanationIcon'
import { Modal } from 'shared/ui/Modal'
import { Select } from 'shared/ui/Select'
import { TextField } from 'shared/ui/TextField'
import { IWerRestrictionPrePost } from 'stores/CalcModelWerStore/WerListOfStationsStore/WerRestrictionsStore/WerRestrictionsStore.types'
import { useStore } from 'stores/useStore.ts'

import cls from './AddAndEditRestrictionModal.module.scss'
import {
  PARAMS_WITH_REQUIRED_VALUE_TYPE,
  TYPES_WITH_REQUIRED_MAX_VALUE,
  TYPES_WITH_REQUIRED_MIN_VALUE,
} from './lib/validation'

interface AddAndEditRestrictionModalProps {
  onClose: () => void
  defaultValue?: IWerRestrictionPrePost
}

export const AddAndEditRestrictionModal: FC<AddAndEditRestrictionModalProps> = observer((props) => {
  const { onClose, defaultValue } = props
  const {
    calcModelWerStore: {
      listOfStationsStore: { restrictionsStore },
    },
  } = useStore()
  const { restrictionOptions, addRestriction, updateRestriction } = restrictionsStore

  const [values, setValues] = useState<Values>(() =>
    defaultValue ? toInitialValue(defaultValue) : getInitialValues(restrictionOptions),
  )
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleChangeValue = (key: string, value: ItemValue) => {
    const newValues = { ...values, [key]: { ...values[key], newValue: value } }
    if (key === 'category' && value !== 'SEASONAL' && value !== 'TEMPORARY') {
      newValues.beginDate.newValue = null
      newValues.stopDate.newValue = null
    }
    if (key === 'type' && !TYPES_WITH_REQUIRED_MIN_VALUE.includes(value as string)) {
      newValues.minValue.newValue = null
    }
    if (key === 'type' && !TYPES_WITH_REQUIRED_MAX_VALUE.includes(value as string)) {
      newValues.maxValue.newValue = null
    }
    if (key === 'parameter' && !PARAMS_WITH_REQUIRED_VALUE_TYPE.includes(value as string)) {
      newValues.valueType.newValue = null
    }
    if (
      (key === 'minValue' && errors['minValue']) ||
      (key === 'maxValue' && errors['maxValue']) ||
      (key === 'comment' && errors['comment']) ||
      !['minValue', 'maxValue', 'comment'].includes(key)
    ) {
      const [_, newErrors] = validation(newValues, errors, key)
      setErrors(newErrors)
    }
    setValues(newValues)
  }

  const handleBlurField = (key: string) => {
    const [_, newErrors] = validation(values, errors, key)
    setErrors(newErrors)
  }

  const save = () => {
    const [isValid, newErrors] = validation(values, errors)
    setErrors(newErrors)
    if (!isValid) return

    const basePayload = {
      id: -1,
      parameter: values.parameter.newValue as string,
      category: values.category.newValue as string,
      type: values.type.newValue as string,
      beginDate: values.beginDate.newValue as Date,
      stopDate: values.stopDate.newValue as Date,
      minValue: values.minValue.newValue !== null ? (Number(values.minValue.newValue) as number) : null,
      maxValue: values.maxValue.newValue !== null ? (Number(values.maxValue.newValue) as number) : null,
      valueType: values.valueType.newValue as string,
      comment: values.comment.newValue as string,
    }
    if (defaultValue) {
      updateRestriction({
        ...basePayload,
        tabId: defaultValue.tabId,
        id: defaultValue.id,
      })
    } else {
      addRestriction(basePayload)
    }
    onClose()
  }

  return (
    <Modal
      title='Создание ограничения'
      className={cls.modal}
      actions={[
        <div key='modal-footer' className={cls.modalFooter}>
          <div className={cls.modalFooterRight}>
            <Button onClick={save}>{defaultValue ? 'Сохранить' : 'Создать'}</Button>
          </div>
        </div>,
      ]}
      onClose={onClose}
      skipConfirmOnClose
    >
      <div className={cls.wrapper}>
        <div className={cls.row}>
          <div className={cls.labelRow}>Параметр</div>
          <div className={cls.valueBlockItem}>
            <Select
              variant='outlined'
              className={cls.select}
              items={restrictionOptions?.parameters}
              value={values.parameter.newValue as string}
              onChange={(value) => handleChangeValue('parameter', value)}
            />
          </div>
        </div>
        <div className={cls.row}>
          <div className={cls.labelRow}>Категория</div>
          <div className={cls.valueBlockItem}>
            <Select
              variant='outlined'
              className={cls.select}
              items={restrictionOptions?.categories}
              value={values.category.newValue as string}
              onChange={(value) => handleChangeValue('category', value)}
            />
          </div>
        </div>
        <div className={cls.row}>
          <div className={cls.labelRow}>Дата начала</div>
          <div className={cls.valueBlockDate}>
            <DatePicker
              className={cls.textFieldInput}
              views={['day', 'month']}
              format={values.category.newValue === 'TEMPORARY' ? DATE_FORMATS.ddMMyyyy : DATE_FORMATS.ddMM}
              disabled={values.category.newValue !== 'SEASONAL' && values.category.newValue !== 'TEMPORARY'}
              value={values.beginDate.newValue as Date}
              setValue={(value) => handleChangeValue('beginDate', value)}
              error={errors?.beginDate}
              showErrorMsg={false}
            />
          </div>
          <ErrorExplanationIcon title={errors?.beginDate} />
        </div>
        <div className={cls.row}>
          <div className={cls.labelRow}>Дата окончания</div>
          <div className={cls.valueBlockDate}>
            <DatePicker
              className={cls.textFieldInput}
              views={['day', 'month']}
              format={values.category.newValue === 'TEMPORARY' ? DATE_FORMATS.ddMMyyyy : DATE_FORMATS.ddMM}
              disabled={values.category.newValue !== 'SEASONAL' && values.category.newValue !== 'TEMPORARY'}
              value={values.stopDate.newValue as Date}
              setValue={(value) => handleChangeValue('stopDate', value as unknown as string)}
              error={errors?.stopDate}
              showErrorMsg={false}
            />
          </div>
          <ErrorExplanationIcon title={errors?.stopDate} />
        </div>
        <div className={cls.row}>
          <div className={cls.labelRow}>Тип ограничения</div>
          <div className={cls.valueBlockItem}>
            <Select
              variant='outlined'
              className={cls.select}
              items={restrictionOptions?.types}
              value={values.type.newValue as string}
              onChange={(value) => handleChangeValue('type', value)}
            />
          </div>
        </div>
        <div className={cls.row}>
          <div className={cls.labelRow}>Минимум</div>
          <div className={cls.valueBlockItem}>
            <TextField
              label={getUnitsMeasurementValue(values.parameter.newValue as string)}
              error={!!errors.minValue?.length}
              value={(values.minValue?.newValue ?? '') as string}
              type='number'
              numberOption={{ positive: true }}
              className={cls.numberFieldInput}
              disabled={!TYPES_WITH_REQUIRED_MIN_VALUE.includes(values.type?.newValue as string)}
              onChange={(e) => handleChangeValue('minValue', e.target.value)}
              onBlur={() => handleBlurField('minValue')}
            />
          </div>
          <ErrorExplanationIcon title={errors?.minValue} />
        </div>
        <div className={cls.row}>
          <div className={cls.labelRow}>Максимум</div>
          <div className={cls.valueBlockItem}>
            <TextField
              label={getUnitsMeasurementValue(values.parameter.newValue as string)}
              error={!!errors.maxValue?.length}
              value={(values.maxValue?.newValue ?? '') as string}
              type='number'
              numberOption={{ positive: true }}
              className={cls.numberFieldInput}
              disabled={!TYPES_WITH_REQUIRED_MAX_VALUE.includes(values.type?.newValue as string)}
              onChange={(e) => handleChangeValue('maxValue', e.target.value)}
              onBlur={() => handleBlurField('maxValue')}
            />
          </div>
          <ErrorExplanationIcon title={errors?.maxValue} />
        </div>
        <div className={cls.row}>
          <div className={cls.labelRow}>Тип значения</div>
          <div className={cls.valueBlockItem}>
            <Select
              variant='outlined'
              className={cls.select}
              items={restrictionOptions?.valueTypes}
              value={values.valueType.newValue as string}
              disabled={!PARAMS_WITH_REQUIRED_VALUE_TYPE.includes(values.parameter.newValue as string)}
              onChange={(value) => handleChangeValue('valueType', value)}
              error={!!errors.valueType?.length}
            />
          </div>
          <ErrorExplanationIcon
            title={formatStr(
              errors?.valueType,
              restrictionOptions?.parameters?.find((v) => v.value === (values.parameter?.newValue as string))?.label ??
                '',
            )}
          />
        </div>
        <div className={cls.row}>
          <div className={cls.labelRow}>Комментарий</div>
          <div className={cls.valueBlockComment}>
            <TextField
              value={(values.comment?.newValue ?? '') as string}
              className={cls.textFieldInput}
              onChange={(e) => handleChangeValue('comment', e.target.value)}
              error={!!errors.comment?.length}
              onBlur={() => handleBlurField('comment')}
            />
          </div>
          <ErrorExplanationIcon title={errors?.comment} />
        </div>
      </div>
    </Modal>
  )
})
