import { endOfDay, isAfter, startOfDay } from 'date-fns'
import { observer } from 'mobx-react'
import { useEffect, useMemo } from 'react'
import { Loader } from 'shared/ui/Loader'
import { useStore } from 'stores/useStore'

import { useNotifyDayChanged } from '../../hooks/useNotifyDayChanged'
import cls from './Parameters.module.scss'
import { Cascade } from './ui/Cascade'
import { DownstreamPlants } from './ui/DownstreamPlants'
import { ReferenceData } from './ui/ReferenceData'
import { UpstreamPlants } from './ui/UpstreamPlants'

export const Parameters = observer(() => {
  const { calcModelWerStore } = useStore()
  const {
    listOfStationsStore: { parametersStore },
    selectedPlant,
    formattedDate,
    date,
  } = calcModelWerStore
  const { resetStore, getPlantCascades, getAvailableCascades, getPlantReferenceData, isLoading, editMode } =
    parametersStore
  const isDayChanged = useNotifyDayChanged()

  const enableEditing = useMemo(
    () => isAfter(startOfDay(date), endOfDay(new Date())) && editMode && !selectedPlant?.viewOnly,
    [date, isDayChanged, selectedPlant, editMode],
  )

  useEffect(() => () => resetStore(), [])

  useEffect(() => {
    getAvailableCascades()
    getPlantCascades()
  }, [selectedPlant])

  useEffect(() => {
    getPlantReferenceData(formattedDate)
  }, [formattedDate, selectedPlant])

  if (!selectedPlant) return <div className={cls.noData}>Выберите станцию</div>

  return (
    <div className={cls.container}>
      {isLoading ? (
        <div className={cls.loaderBlock}>
          <Loader />
        </div>
      ) : (
        <>
          <ReferenceData />
          <Cascade isEditingEnabled={enableEditing} />
          <UpstreamPlants isEditingEnabled={enableEditing} />
          <DownstreamPlants isEditingEnabled={enableEditing} />
        </>
      )}
    </div>
  )
})
