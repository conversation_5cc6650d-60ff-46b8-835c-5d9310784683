.fullHeight {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.periodPickerContainer {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
}

.dateRangePickerContainer {
  width: 230px;
  margin-top: -8px; // Костыль для центрирования по вертикали из-за реализации DateRangePicker
}

.periodButtonsContainer {
  display: flex;
  gap: 8px;
}

.periodButton {
  width: 180px;
}

.iconNext {
  transform: rotate(180deg);
}

.tableWrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.actionHeader {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.addIcon {
  stroke: var(--primary-color);
  stroke-width: 0.5;
}

.iconBlueBtn {
  color: var(--primary-color);
}

.iconRedBtn {
  color: var(--red-color);
}

.actions {
  width: 220px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.noData {
  background-color: var(--background-color-secondary);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  color: var(--text-gray);
}
