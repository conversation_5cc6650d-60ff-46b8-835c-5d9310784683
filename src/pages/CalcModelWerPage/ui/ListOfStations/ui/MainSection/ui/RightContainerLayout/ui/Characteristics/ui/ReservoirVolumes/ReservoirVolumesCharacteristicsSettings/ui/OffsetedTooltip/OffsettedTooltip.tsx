import { Tooltip } from '@mui/material'
import { ReactNode } from 'react'
import { Icon } from 'shared/ui/Icon'

import cls from './OffsettedTooltip.module.scss'

interface OffsetedTooltipProps {
  title: ReactNode
  offset?: [number, number]
}

const defaultOffset = [0, 0] as [number, number]

export const OffsettedTooltip = ({ title, offset = defaultOffset }: OffsetedTooltipProps) => {
  return (
    <Tooltip
      title={title}
      slotProps={{
        tooltip: {
          sx: {
            maxWidth: 'none',
          },
        },
        popper: {
          modifiers: [
            {
              name: 'offset',
              options: {
                offset,
              },
            },
          ],
        },
      }}
    >
      <div className={cls.infoIcon}>
        <Icon name='information' width={16} height={16} />
      </div>
    </Tooltip>
  )
}
