import { observer } from 'mobx-react'
import { TextField } from 'shared/ui/TextField'
import { useStore } from 'stores/useStore'

import cls from './ReferenceData.module.scss'

export const ReferenceData = observer(() => {
  const { calcModelWerStore } = useStore()
  const {
    listOfStationsStore: {
      parametersStore: { plantReferenceData },
    },
  } = calcModelWerStore

  return (
    <>
      {plantReferenceData.map((el) => {
        return (
          <div className={cls.row} key={el.parameterName}>
            <div className={cls.rowLabel}>{el.description}</div>
            <TextField
              disabled
              label={el?.measUnit ?? null}
              className={cls.rowTextField}
              value={el.parameterValue?.value ?? ''}
            />
          </div>
        )
      })}
    </>
  )
})
