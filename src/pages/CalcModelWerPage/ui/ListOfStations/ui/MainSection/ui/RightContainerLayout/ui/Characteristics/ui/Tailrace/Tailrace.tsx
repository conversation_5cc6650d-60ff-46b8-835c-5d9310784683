import { observer } from 'mobx-react'
import { useEffect } from 'react'
import { useStore } from 'stores/useStore'

import cls from './Tailrace.module.scss'
import { TailraceActualMeasurementsLoader } from './TailraceActualMeasurementsLoader'
import { TailraceCharacteristicsChart } from './TailraceCharacteristicsChart'
import { TailraceCharacteristicsSettings } from './TailraceCharacteristicsSettings'
import { TailraceCharacteristicsSpreadsheet } from './TailraceCharacteristicsSpreadsheet'
import { TailraceDownstreamSelector } from './TailraceDownstreamSelector'

export const Tailrace = observer(() => {
  const {
    calcModelWerStore: {
      listOfStationsStore: {
        characteristicsStore: {
          tailraceStore: { resetStore },
        },
      },
    },
  } = useStore()

  useEffect(() => {
    return () => resetStore()
  }, [])

  return (
    <div className={cls.container}>
      <div className={cls.actionContainer}>
        <div className={cls.settingsContainer}>
          <TailraceDownstreamSelector />
          <TailraceCharacteristicsSettings />
        </div>
        <TailraceActualMeasurementsLoader />
      </div>
      <div className={cls.mainSection}>
        <div className={cls.spreadsheetContainer} id='spreadsheetContainer'>
          <TailraceCharacteristicsSpreadsheet />
        </div>
        <div className={cls.chartContainer}>
          <TailraceCharacteristicsChart />
        </div>
      </div>
    </div>
  )
})
