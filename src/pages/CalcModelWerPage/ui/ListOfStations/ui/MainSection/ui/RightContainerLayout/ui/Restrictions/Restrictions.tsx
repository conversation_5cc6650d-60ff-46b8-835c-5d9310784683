import { I<PERSON><PERSON><PERSON>on, Tooltip } from '@mui/material'
import { endOfDay, format, isAfter, startOfDay } from 'date-fns'
import { IWerRestriction } from 'entities/api/calcModelPage.entities.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import { AddAndEditRestrictionModal } from 'pages/CalcModelWerPage/ui/ListOfStations/ui/MainSection/ui/RightContainerLayout/ui/Restrictions/ui/AddAndEditRestrictionModal'
import { useEffect, useMemo, useRef, useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { formatDate, readableISO8601DateMonth } from 'shared/lib/dateFormates'
import { AccessControl } from 'shared/ui/AccessControl'
import { Button } from 'shared/ui/Button'
import { DateRangePicker } from 'shared/ui/DateRangePicker'
import { Icon } from 'shared/ui/Icon'
import { IWerRestrictionPrePost } from 'stores/CalcModelWerStore/WerListOfStationsStore/WerRestrictionsStore/WerRestrictionsStore.types'
import { useStore } from 'stores/useStore.ts'
import { Table } from 'widgets/Table'

import { useNotifyDayChanged } from '../../hooks/useNotifyDayChanged'
import cls from './Restriction.module.scss'

export const Restrictions = observer(() => {
  const tableContainerRef = useRef<HTMLDivElement>(null)
  const [isAddModal, setIsAddModal] = useState<boolean>(false)
  const [editableRestriction, setEditableRestriction] = useState<IWerRestrictionPrePost | null>(null)
  const [height, setHeight] = useState<number | null>(null)
  const isDayChanged = useNotifyDayChanged()
  const {
    calcModelWerStore: {
      date,
      selectedPlant,
      listOfStationsStore: { restrictionsStore },
    },
  } = useStore()

  const {
    isLoading,
    restrictions,
    originalRestrictions,
    period,
    init,
    deleteRestriction,
    reset,
    resetChanges,
    setPeriodAndFetch,
    handlePrevPeriod,
    handleNextPeriod,
  } = restrictionsStore

  const disableAddEditRestriction = useMemo(
    () => !isAfter(startOfDay(date), endOfDay(new Date())) || selectedPlant?.viewOnly,
    [date, isDayChanged, selectedPlant],
  )

  const changeHeightTable = () => {
    if (tableContainerRef.current) {
      setHeight(tableContainerRef.current.getBoundingClientRect().height)
    }
  }

  const handlePeriodChange = (value: [Date | null, Date | null]) => {
    if (value[0] && value[1] && selectedPlant?.plantId) {
      setPeriodAndFetch([value[0], value[1]], selectedPlant.plantId, format(date, 'yyyy-MM-dd'))
    }
  }

  useEffect(() => {
    if (!selectedPlant?.plantId) return

    init(selectedPlant.plantId, format(date, 'yyyy-MM-dd'))
  }, [selectedPlant, date])

  useEffect(() => {
    changeHeightTable()
    window.addEventListener('resize', changeHeightTable)

    return () => {
      reset()
      resetChanges()
      window.removeEventListener('resize', changeHeightTable)
    }
  }, [])

  const columns = [
    {
      name: 'parameter',
      title: 'Параметр',
      width: 135,
      editingEnabled: false,
      render: (value: IWerRestriction['parameter']) => value.title,
    },
    {
      name: 'category',
      title: 'Категория',
      width: 100,
      editingEnabled: false,
      render: (value: IWerRestriction['category']) => value.title,
    },
    {
      name: 'type',
      title: 'Тип ограничения',
      width: 130,
      editingEnabled: false,
      render: (value: IWerRestriction['type']) => value.title,
    },
    {
      name: 'beginDate',
      title: 'Дата начала',
      width: 120,
      editingEnabled: false,
      render: (_: IWerRestriction['beginDate'], row: IWerRestriction) => {
        if (row.category.code === 'TEMPORARY' && row.temporaryBeginDate) {
          return formatDate(row.temporaryBeginDate)
        } else if (row.beginDate) {
          return readableISO8601DateMonth(row.beginDate)
        }

        return ''
      },
    },
    {
      name: 'stopDate',
      title: 'Дата окончания',
      width: 120,
      editingEnabled: false,
      render: (_: IWerRestriction['stopDate'], row: IWerRestriction) => {
        if (row.category.code === 'TEMPORARY' && row.temporaryEndDate) {
          return formatDate(row.temporaryEndDate)
        } else if (row.stopDate) {
          return readableISO8601DateMonth(row.stopDate)
        }

        return ''
      },
    },
    {
      name: 'minValue',
      title: 'Мин.',
      width: 80,
      editingEnabled: false,
      render: (value: IWerRestriction['minValue']) => value || '',
    },
    {
      name: 'maxValue',
      title: 'Макс.',
      width: 80,
      editingEnabled: false,
      render: (value: IWerRestriction['maxValue']) => value || '',
    },
    {
      name: 'valueType',
      title: 'Тип значения',
      width: 150,
      editingEnabled: false,
      render: (value: IWerRestriction['valueType']) => (value ? value.title : ''),
    },
    {
      name: 'comment',
      title: 'Комментарий',
      width: 200,
      editingEnabled: false,
      render: (value: IWerRestriction['comment']) => value || '',
    },
    {
      name: 'actions',
      title: '',
      width: 80,
      editingEnabled: false,
      headRender: () => {
        return (
          <AccessControl rules={[ROLES.TECH_ADMIN_CM]}>
            <div className={cls.actionHeader}>
              <Tooltip title='Создать ограничение'>
                <span>
                  <IconButton
                    sx={{ color: 'var(--primary-color)', display: 'inline-flex!important' }}
                    onClick={() => {
                      setIsAddModal(true)
                    }}
                    disabled={disableAddEditRestriction}
                  >
                    <Icon className={cls.addIcon} name='plus' width={13} />
                  </IconButton>
                </span>
              </Tooltip>
            </div>
          </AccessControl>
        )
      },
      render: (_: never, value: IWerRestrictionPrePost) => {
        return (
          <AccessControl rules={[ROLES.TECH_ADMIN_CM]}>
            <div className={cls.actions}>
              <Tooltip title='Настроить ограничение'>
                <span>
                  <IconButton
                    onClick={() => {
                      setEditableRestriction(value)
                    }}
                    className={classNames(cls.iconBlueBtn)}
                    disabled={disableAddEditRestriction}
                  >
                    <Icon name='settings' width={13} height={13} />
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip title='Удалить ограничение'>
                <span>
                  <IconButton
                    onClick={() => {
                      deleteRestriction(value)
                    }}
                    className={cls.iconRedBtn}
                    disabled={disableAddEditRestriction}
                  >
                    <Icon name='trash' width={13} height={13} />
                  </IconButton>
                </span>
              </Tooltip>
            </div>
          </AccessControl>
        )
      },
    },
  ]

  const handleClose = () => {
    setIsAddModal(false)
    setEditableRestriction(null)
  }

  if (!selectedPlant) {
    return <div className={cls.noData}>Выберите станцию</div>
  }

  if (!period) {
    return <div className={cls.noData}>Загрузка...</div>
  }

  return (
    <>
      <div className={cls.fullHeight}>
        <div className={cls.periodPickerContainer}>
          <div className={cls.dateRangePickerContainer}>
            <DateRangePicker dateFrom={period[0]} dateTo={period[1]} handleChangeDate={handlePeriodChange} />
          </div>

          <div className={cls.periodButtonsContainer}>
            <Button variant='outlined' onClick={handlePrevPeriod} className={cls.periodButton}>
              <Icon width={12} name='arrowLeft' />
              Предыдущий период
            </Button>
            <Button variant='outlined' onClick={handleNextPeriod} className={cls.periodButton}>
              Следующий период
              <Icon width={12} className={cls.iconNext} name='arrowLeft' />
            </Button>
          </div>
        </div>

        <div ref={tableContainerRef} className={cls.tableWrapper}>
          <Table
            rows={restrictions}
            columns={columns}
            height={height ?? 500}
            initialData={originalRestrictions ?? []}
            editMode
            columnSearchDisabled={[
              'parameter',
              'category',
              'type',
              'beginDate',
              'stopDate',
              'minValue',
              'maxValue',
              'valueType',
              'comment',
              'actions',
            ]}
            loading={isLoading}
          />
        </div>
      </div>

      {isAddModal && <AddAndEditRestrictionModal onClose={handleClose} />}
      {editableRestriction && <AddAndEditRestrictionModal onClose={handleClose} defaultValue={editableRestriction} />}
    </>
  )
})
