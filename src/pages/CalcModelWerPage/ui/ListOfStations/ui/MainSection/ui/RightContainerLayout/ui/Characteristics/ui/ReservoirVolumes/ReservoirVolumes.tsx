import { observer } from 'mobx-react'
import { useEffect } from 'react'
import { useStore } from 'stores/useStore'

import cls from './ReservoirVolumes.module.scss'
import { ReservoirVolumesCharacteristicsChart } from './ReservoirVolumesCharacteristicsChart'
import { ReservoirVolumesCharacteristicsSettings } from './ReservoirVolumesCharacteristicsSettings'
import { ReservoirVolumesCharacteristicsSpreadsheet } from './ReservoirVolumesCharacteristicsSpreadsheet'
import { RVCharacteristicsVbLevelsSelect } from './RVCharacteristicsVbLevelsSelect'

export const ReservoirVolumes = observer(() => {
  const {
    calcModelWerStore: {
      listOfStationsStore: {
        characteristicsStore: {
          reservoirVolumeStore: { resetStore },
        },
      },
    },
  } = useStore()

  useEffect(() => {
    return () => resetStore()
  }, [])

  return (
    <div className={cls.container}>
      <div className={cls.leftSection}>
        <div className={cls.leftSectionSettings}>
          <RVCharacteristicsVbLevelsSelect />
          <ReservoirVolumesCharacteristicsSettings />
        </div>
        <ReservoirVolumesCharacteristicsSpreadsheet />
      </div>

      <div className={cls.rightSection}>
        <ReservoirVolumesCharacteristicsChart />
      </div>
    </div>
  )
})
