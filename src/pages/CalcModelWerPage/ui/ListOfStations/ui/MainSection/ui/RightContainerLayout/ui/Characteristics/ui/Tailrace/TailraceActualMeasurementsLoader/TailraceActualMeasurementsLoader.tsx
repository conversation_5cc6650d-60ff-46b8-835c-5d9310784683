import { format } from 'date-fns'
import { observer } from 'mobx-react'
import { useState } from 'react'
import { useAbortController } from 'shared/hooks'
import { Button } from 'shared/ui/Button'
import { DateRangePicker } from 'shared/ui/DateRangePicker'
import { DataPickerValue, DatePickerContext } from 'shared/ui/DateRangePicker/ui/DateRangePicker'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Modal } from 'shared/ui/Modal'
import { useStore } from 'stores/useStore'

import cls from './TailraceActualMeasurementsLoader.module.scss'

type DateRange = {
  fromDate: Date | null
  toDate: Date | null
}

export const TailraceActualMeasurementsLoader = observer(() => {
  const {
    calcModelWerStore: {
      formattedDate,
      isSelectedDateEditable,
      selectedPlant,
      isSelectedPlantViewOnly,
      listOfStationsStore: {
        characteristicsStore: {
          editMode,
          tailraceStore: { isEditRows, getActualData, isActualDataLoading },
        },
      },
    },
  } = useStore()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [dateRange, setDateRange] = useState<DateRange>(() => ({
    fromDate: new Date(),
    toDate: new Date(),
  }))

  const { getSignal, abort } = useAbortController()

  const isOpenModalButtonDisabled = !editMode || !isSelectedDateEditable || isSelectedPlantViewOnly || isEditRows

  const handleLoadActualMeasurementsData = async () => {
    const plantId = selectedPlant?.plantId
    const date = formattedDate
    const { fromDate, toDate } = dateRange

    if (!plantId || !fromDate || !toDate) return

    const startDate = format(fromDate, 'yyyy-MM-dd')
    const endDate = format(toDate, 'yyyy-MM-dd')

    const signal = getSignal()

    try {
      await getActualData(plantId, date, startDate, endDate, signal)
      setIsModalOpen(false)
    } catch (error) {
      console.error('Ошибка при загрузке данных', error)
    }
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    // При закрытии модального окна отменяем запрос, если он выполняется
    abort()
  }

  const handleChangeDate = (value: DataPickerValue, context: DatePickerContext) => {
    if (context.validationError.some((item) => item)) return
    const [fromDate, toDate] = value
    setDateRange({
      fromDate,
      toDate,
    })
  }

  return (
    <div className={cls.buttonContainer}>
      <Button variant='outlined' onClick={() => setIsModalOpen(true)} disabled={isOpenModalButtonDisabled}>
        Загрузить фактические данные
      </Button>

      <Modal
        open={isModalOpen}
        onClose={handleCloseModal}
        title='Загрузка фактических данных'
        actions={
          <div className={cls.actionContainer}>
            <LoadingButton
              variant='contained'
              onClick={handleLoadActualMeasurementsData}
              loading={isActualDataLoading}
              className={cls.loadingButton}
            >
              Загрузить
            </LoadingButton>
          </div>
        }
      >
        <div className={cls.modalBody}>
          <h2 className={cls.titleStation}>{selectedPlant!.name}</h2>
          <div className={cls.rangeBody}>
            <p>Период</p>
            <div className={cls.dateRangePickerContainer}>
              <DateRangePicker
                dateFrom={dateRange.fromDate}
                dateTo={dateRange.toDate}
                handleChangeDate={handleChangeDate}
              />
            </div>
          </div>
        </div>
      </Modal>
    </div>
  )
})
