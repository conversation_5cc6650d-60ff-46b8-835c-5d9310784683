import { isAfter, isEqual } from 'date-fns'
import { Values } from 'pages/ReportsPage/ui/ModalCreateEditReport/config/types'

type IErrorObject = Record<string, string>
type IValidator = (allValues: Values, errors: IErrorObject) => IErrorObject
type IValidationResult = [boolean, IErrorObject]

export const PARAMS_WITH_REQUIRED_VALUE_TYPE = [
  'FLOW',
  'TURBINE_FLOW',
  'BYPASS_FLOW',
  'POWER',
  'OUTPUT',
  'DELTA_FLOW',
  'DELTA_TURBINE_FLOW',
  'DELTA_BYPASS_FLOW',
  'DELTA_POWER',
  'DELTA_OUTPUT',
]
export const TYPES_WITH_REQUIRED_MIN_VALUE = ['GREATER', 'GREATER_OR_EQUAL', 'BETWEEN']
export const TYPES_WITH_REQUIRED_MAX_VALUE = ['LESS', 'LESS_OR_EQUAL', 'BETWEEN']

const MAX_COMMENT_LENGTH = 300

const fieldsValidation: Record<string, IValidator> = {
  comment: (values, errors) => {
    if (errors['comment']) delete errors.comment
    if ((values.comment?.newValue as string)?.length > MAX_COMMENT_LENGTH) {
      errors['comment'] = `Максимальная длина комментария ${MAX_COMMENT_LENGTH} символов`
    }

    return errors
  },
  valueType: (values, errors) => {
    const parameter = values.parameter?.newValue as string
    if (errors['valueType']) delete errors.valueType
    if (PARAMS_WITH_REQUIRED_VALUE_TYPE.includes(parameter) && !values.valueType?.newValue) {
      errors['valueType'] = "Для параметра '{{s}}' должен быть установлен тип значения"
    }

    return errors
  },
  parameter: (values, errors) => {
    const parameter = values.parameter?.newValue as string
    if (!PARAMS_WITH_REQUIRED_VALUE_TYPE.includes(parameter) && errors['valueType']) delete errors.valueType

    return errors
  },
  beginDate: (values, errors) => {
    const beginDate = values.beginDate?.newValue
    const stopDate = values.stopDate?.newValue

    if (errors['beginDate']) delete errors.beginDate
    if (values.category?.newValue === 'SEASONAL' || values.category?.newValue === 'TEMPORARY') {
      const categoryName = values.category?.newValue === 'SEASONAL' ? 'сезонных' : 'временных'
      if (beginDate === null) {
        errors['beginDate'] = `Дата начала действия должна быть установлена для ${categoryName} ограничений`
      }
      if (beginDate !== null && stopDate !== null) {
        if (isEqual(beginDate as Date, stopDate as Date)) {
          errors['beginDate'] = `Даты начала и окончания для ${categoryName} ограничений должны отличаться`
          errors['stopDate'] = errors['beginDate']
        } else if (isAfter(beginDate as Date, stopDate as Date)) {
          errors['beginDate'] = `Дата начала должна быть раньше даты окончания для ${categoryName} ограничений`
          errors['stopDate'] = `Дата окончания должна быть позже даты начала для ${categoryName} ограничений`
        } else {
          if (errors['stopDate']) delete errors.stopDate
          if (errors['beginDate']) delete errors.beginDate
        }
      }
    }

    return errors
  },
  stopDate: (values, errors) => {
    const beginDate = values.beginDate?.newValue
    const stopDate = values.stopDate?.newValue

    if (errors['stopDate']) delete errors.stopDate
    if (values.category?.newValue === 'SEASONAL' || values.category?.newValue === 'TEMPORARY') {
      const categoryName = values.category?.newValue === 'SEASONAL' ? 'сезонных' : 'временных'
      if (stopDate === null) {
        errors['stopDate'] = `Дата окончания действия должна быть установлена для ${categoryName} ограничений`
      }
      if (beginDate !== null && stopDate !== null) {
        if (isEqual(beginDate as Date, stopDate as Date)) {
          errors['beginDate'] = `Даты начала и окончания для ${categoryName} ограничений должны отличаться`
          errors['stopDate'] = errors['beginDate']
        } else if (isAfter(beginDate as Date, stopDate as Date)) {
          errors['beginDate'] = `Дата начала должна быть раньше даты окончания для ${categoryName} ограничений`
          errors['stopDate'] = `Дата окончания должна быть позже даты начала для ${categoryName} ограничений`
        } else {
          if (errors['stopDate']) delete errors.stopDate
          if (errors['beginDate']) delete errors.beginDate
        }
      }
    }

    return errors
  },
  category: (values, errors) => {
    if (values.category?.newValue !== 'SEASONAL' && values.category?.newValue !== 'TEMPORARY') {
      if (errors['beginDate']) delete errors.beginDate
      if (errors['stopDate']) delete errors.stopDate
    }

    return errors
  },
  type: (_, errors) => {
    if (errors['maxValue']) delete errors.maxValue
    if (errors['minValue']) delete errors.minValue

    return errors
  },
  minValue: (values, errors) => {
    const minValue = parseFloat(values.minValue?.newValue as string)
    const maxValue = parseFloat(values.maxValue?.newValue as string)
    const type = values.type?.newValue as string

    if (errors['minValue']) delete errors.minValue
    if (TYPES_WITH_REQUIRED_MIN_VALUE.includes(type) && Number.isNaN(minValue)) {
      errors['minValue'] = "Минимальное значение должно быть задано для типов ограничений '>', '>=', 'в пределах'"
    }
    if (type === 'BETWEEN' && !Number.isNaN(maxValue) && !Number.isNaN(minValue)) {
      if (maxValue < minValue) {
        errors['minValue'] = 'Минимальное значение ограничения не может быть больше максимального'
        errors['maxValue'] = errors['minValue']
      } else {
        if (errors['minValue']) delete errors.minValue
        if (errors['maxValue']) delete errors.maxValue
      }
    }

    return errors
  },
  maxValue: (values, errors) => {
    const minValue = parseFloat(values.minValue?.newValue as string)
    const maxValue = parseFloat(values.maxValue?.newValue as string)
    const type = values.type?.newValue as string

    if (errors['maxValue']) delete errors.maxValue
    if (TYPES_WITH_REQUIRED_MAX_VALUE.includes(type) && Number.isNaN(maxValue)) {
      errors['maxValue'] = "Максимальное значение должно быть задано для типов ограничений '<', '<=', 'в пределах'"
    }
    if (type === 'BETWEEN' && !Number.isNaN(maxValue) && !Number.isNaN(minValue)) {
      if (maxValue < minValue) {
        errors['minValue'] = 'Минимальное значение ограничения не может быть больше максимального'
        errors['maxValue'] = errors['minValue']
      } else {
        if (errors['minValue']) delete errors.minValue
        if (errors['maxValue']) delete errors.maxValue
      }
    }

    return errors
  },
}

export const validation = (values: Values, errors: IErrorObject, key?: string): IValidationResult => {
  let newErrorObject = { ...errors }
  const allValidationKeys = Object.keys(fieldsValidation)
  /** если есть ключ - валидация проводится только для одного поля, если нет - для всех */
  if (key) {
    newErrorObject = fieldsValidation[key](values, newErrorObject)

    return [!Object.keys(newErrorObject).length, newErrorObject]
  } else {
    allValidationKeys.forEach((k) => {
      newErrorObject = fieldsValidation[k](values, newErrorObject)
    })

    return [!Object.keys(newErrorObject).length, newErrorObject]
  }
}
