interface ChartPoint {
  x: number | null
  y: number | null
  rowNumber: number
}

// Функция-хелпер для преобразования значений координат для графика
const getNumberValue = (value?: number | string): number | null => {
  if (value === undefined || value === '') return null

  return Number(value)
}

/**
 * Функция предназначена для преобразования исходного двумерного массива данных
 * (каждая строка массива имеет вид [значение для расхода воды, значение для уровня НБ]
 * в массив точек для графика, где каждая точка представлена объектом вида { x, y, rowNumber }:
 *
 * - x: координата из столбца расхода воды ГЭС
 * - y: координата из столбца уровня нижнего бьефа
 * - rowNumber: номер строки
 *
 * При этом, если значение в ячейке отсутствует (то есть оно undefined, пустая строка
 * или иное «ложное» значение), то вместо числа возвращается null. Это делается для того, чтобы:
 *
 * • Highcharts видел, что в данной точке нет данных, и корректно обрабатывал пропуски.
 * • Избежать автоматического преобразования отсутствующего значения в 0, что могло бы исказить график.
 *
 * @param data - двумерный массив данных из таблицы
 * @param hasFreezingWatch - флаг наличия данных с учетом ледостава (для второго типа графика)
 * @returns массив серий данных для графика
 */
export const mapSpreadsheetDataToPoints = (
  data: (number | string | undefined)[][],
  hasFreezingWatch = false,
): ChartPoint[][] => {
  const series: ChartPoint[][] = []

  // Серия для уровня НБ без учета ледостава (таблица из 2х столбцов)
  const mainSeries: ChartPoint[] = []

  // Серия для уровня НБ с учетом ледостава (таблица из 3х столбцов)
  const freezingSeries: ChartPoint[] | null = hasFreezingWatch ? [] : null

  data.forEach((row, rowIndex) => {
    const x = getNumberValue(row[0]) // Расход воды (1-й столбец)
    const y1 = getNumberValue(row[1]) // Уровень НБ (2-й столбец)

    mainSeries.push({
      x,
      y: y1,
      rowNumber: rowIndex + 1,
    })

    if (hasFreezingWatch && freezingSeries) {
      const y2 = getNumberValue(row[2]) // Уровень НБ с учетом ледостава (3-й столбец)

      freezingSeries.push({
        x,
        y: y2,
        rowNumber: rowIndex + 1,
      })
    }
  })

  // Определяем индекс последней значимой точки (где X не null)
  let lastFilledIndex = mainSeries.length - 1
  while (lastFilledIndex >= 0 && mainSeries[lastFilledIndex].x === null) {
    lastFilledIndex--
  }

  // Отсекаем "хвостовые" строки, в которых после последней заполненной строки все значения null,
  // если таковые имеются.
  // Важно! Без данной манипуляции в случае большой выборки данных и наличии пустых строк
  // график "ломается" (пропадает yAxis и сам график). А если отсекать все строки со значениями null, то
  // в случае наличия промежуточных пустых строк, произойдёт слипание графика (последняя до разрыва строка
  // соединится линией с первой после разрыва)
  const trimmedMainSeries = mainSeries.slice(0, lastFilledIndex + 1)
  series.push(trimmedMainSeries)

  // Если есть серия с учетом ледостава, обрезаем по lastMainIndex
  if (hasFreezingWatch && freezingSeries) {
    const trimmedFreezingSeries = freezingSeries.slice(0, lastFilledIndex + 1)
    series.push(trimmedFreezingSeries)
  }

  return series
}

/**
 * Функция для обработки данных таблицы с нижележащими ГЭС с включенным "Подпором"
 * Создает отдельную серию точек для каждого уровня нижележащей ГЭС
 *
 * @param data - двумерный массив данных из таблицы
 * @param downstreamPlantLevels - массив уровней нижележащих ГЭС
 * @returns массив серий данных для графика, по одной серии на каждый уровень
 */
export const mapSpreadsheetDataToPointsForDownstream = (
  data: (number | string | undefined)[][],
  downstreamPlantLevels: (string | number)[],
): ChartPoint[][] => {
  // Массив серий, по одной для каждого уровня нижележащей ГЭС
  const series: ChartPoint[][] = downstreamPlantLevels.map(() => [])

  data.forEach((row, rowIndex) => {
    const x = getNumberValue(row[0]) // Расход воды (первый столбец)

    // Обрабатываем каждый уровень, начиная со второго столбца
    downstreamPlantLevels.forEach((_, levelIndex) => {
      // +1 потому что первый столбец это X, а Y начинаются со второго столбца
      const yValue = row[levelIndex + 1]
      const y = getNumberValue(yValue)

      series[levelIndex].push({
        x,
        y,
        rowNumber: rowIndex + 1,
      })
    })
  })

  // Для каждой серии находим последний значимый индекс и обрезаем хвостовые null значения
  return series.map((seriesPoints) => {
    let lastFilledIndex = seriesPoints.length - 1
    while (lastFilledIndex >= 0 && seriesPoints[lastFilledIndex].x === null) {
      lastFilledIndex--
    }

    return seriesPoints.slice(0, lastFilledIndex + 1)
  })
}
