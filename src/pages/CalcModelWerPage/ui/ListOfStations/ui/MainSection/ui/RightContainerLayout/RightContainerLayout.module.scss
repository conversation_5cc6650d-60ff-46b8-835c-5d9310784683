.right {
  height: 100%;
  width: calc(100% - 14.2rem);
  margin-left: 0.2rem;
  background-color: var(--background-color-secondary);
  border-radius: 8px;
  box-shadow:
    0 2px 3px 0 rgb(0 0 0 / 5%),
    0 1px 2px 0 rgb(0 0 0 / 10%);
}

.dataContainer {
  width: 100%;
  height: 100%;
  padding: 0.2rem;
  display: flex;
  flex-direction: column;
}

.headerRight {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 40px;
  padding: 0.5em 1em;
  background-color: var(--background-color-primary);
  border-radius: 8px;
  color: var(--text-color);

  & > div {
    // у контейнера три потомка - заголовок наименование станции, табы и кнопки сохранения/сброса редактирования, поэтому деление на три равных по ширине блока
    flex: 1;
  }
}

.headerTitle {
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  color: var(--text-color);
}

.toggleButtons {
  display: flex;
  justify-content: center;
}

.actions {
  width: 220px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 0.75rem;
}
