import Handsontable from 'handsontable'
import { observer } from 'mobx-react'
import { formatReservoirVolumesNumericValue } from 'stores/CalcModelWerStore/WerListOfStationsStore/WerCharacteristicsStore/WerReservoirVolumeStore/lib'
import { useStore } from 'stores/useStore'
import { handlePastSpreadsheetCellValue } from 'widgets/Spreadsheet/ui/lib'
import { SpreadsheetReact } from 'widgets/SpreadsheetReact'
import { SpreadsheetReactProps } from 'widgets/SpreadsheetReact/SpreadsheetReact'

import cls from './ReservoirVolumesCharacteristicsSpreadsheet.module.scss'

type GridSettings = Handsontable.GridSettings

export const ReservoirVolumesCharacteristicsSpreadsheet = observer(() => {
  const { calcModelWerStore } = useStore()
  const { listOfStationsStore } = calcModelWerStore
  const { characteristicsStore } = listOfStationsStore
  const { reservoirVolumeStore } = characteristicsStore
  const { characteristicsDataSpreadsheet, updateCharacteristicsSpreadsheetData, isCharacteristicsLoaded } =
    reservoirVolumeStore
  const { data, cell, nestedHeaders, columns, rowHeaders } = characteristicsDataSpreadsheet

  const colHeaders = nestedHeaders.flatMap((row) => row.map((header) => header.label))

  const beforePaste: GridSettings['beforePaste'] = (data, _) => {
    const processedData = data
      .map((row) =>
        row
          .map((cellValue) =>
            handlePastSpreadsheetCellValue(cellValue, (cellValue) =>
              formatReservoirVolumesNumericValue(cellValue, true),
            ),
          )
          .filter((cellValue) => cellValue !== undefined),
      )
      .filter((row) => row.length > 0)

    if (processedData.length) {
      // Очищаем оригинальный массив и вставляем обработанные данные
      data.length = 0
      data.push(...processedData)
    } else {
      return false
    }
  }

  /**
   * Стилизует заголовки столбцов
   * @param col - Индекс столбца.
   * @param TH - Элемент заголовка столбца.
   * @param level - Уровень вложенности.
   */
  const afterGetColHeader: GridSettings['afterGetColHeader'] = (col, TH) => {
    const isNumberColumn = col === -1
    let headerText = isNumberColumn ? '№' : (TH.textContent ?? '')

    // Если не нумерная колонка и текст содержит запятую, разбиваем его на две строки.
    if (!isNumberColumn && headerText.includes(',')) {
      headerText = headerText.replace(/,\s*/, ',<br/>')
    }

    // Сбрасываем стили перед добавлением новых
    TH.innerHTML = ''

    const div = document.createElement('div')
    div.style.cssText = `
      height: 60px;
      line-height: normal;
      white-space: normal;
      word-wrap: break-word;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
    `

    const hasError = cell.some((cellItem) => Boolean(cellItem.comment))
    if (hasError) {
      TH.classList.add('isNotValid')
    }

    div.innerHTML = headerText
    TH.appendChild(div)

    TH.classList.add(cls.bold)
  }

  if (!isCharacteristicsLoaded) return null

  const tableSettings: Partial<SpreadsheetReactProps> = {
    data,
    columns,
    rowHeaders,
    colHeaders,
    cell,
    height: 685,
    width: 273,
    rowHeaderWidth: 45,
    colWidths: 108,
    maxRows: data.length,
    afterChange: updateCharacteristicsSpreadsheetData,
    beforePaste,
    afterGetColHeader,
  }

  return <SpreadsheetReact {...tableSettings} />
})
