import { IWerDepartment } from 'entities/api/calcModelWerManager.entities'

export const renderWerDepartments = (werDepartments: IWerDepartment[]) => {
  if (!werDepartments || werDepartments.length === 0) return null

  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      {werDepartments.map((werDepartment) => (
        <div key={werDepartment.id}>{werDepartment.name}</div>
      ))}
    </div>
  )
}
