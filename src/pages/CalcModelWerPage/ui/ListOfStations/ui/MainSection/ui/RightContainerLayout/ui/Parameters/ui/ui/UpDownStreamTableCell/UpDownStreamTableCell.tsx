import { FC } from 'react'
import { classNames } from 'shared/lib/classNames'

import cls from './UpDownStreamTableCell.module.scss'

interface UpDownStreamTableCellProps {
  value?: number | string | null
  className?: string
}

export const UpDownStreamTableCell: FC<UpDownStreamTableCellProps> = (props) => {
  const { value, className } = props

  return <div className={classNames(cls.tableCell, {}, [className])}>{value}</div>
}
