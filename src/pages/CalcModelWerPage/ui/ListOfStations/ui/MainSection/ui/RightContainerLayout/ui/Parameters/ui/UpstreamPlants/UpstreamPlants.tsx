import { Icon<PERSON><PERSON><PERSON>, Toolt<PERSON> } from '@mui/material'
import { format, startOfDay } from 'date-fns'
import { IWerDepartment } from 'entities/api/calcModelWerManager.entities'
import { observer } from 'mobx-react'
import {
  validatePlantEndDate,
  validatePlantStartDate,
} from 'pages/CalcModelWerPage/ui/ListOfStations/ui/MainSection/ui/RightContainerLayout/ui/Parameters/lib/validatePlantDate.ts'
import { FC, useEffect, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { prepareDate } from 'shared/lib/prepareData'
import { Icon } from 'shared/ui/Icon'
import { useStore } from 'stores/useStore'
import { IColumn, TableV1 } from 'widgets/TableV1'

import { renderWerDepartments } from '../../lib/renderFuctions'
import { AddPlantModal } from '../ui/AddPlantModal'
import { UpDownStreamTableCell } from '../ui/UpDownStreamTableCell'
import cls from './UpstreamPlants.module.scss'

interface IUpstreamPlantsRow {
  tabId: number
  plantName: string | null
  travelTime: number | null
  departmentName: IWerDepartment[]
  startDate: string
  endDate: string | null
  type: string
}

interface Props {
  isEditingEnabled: boolean
}

export const UpstreamPlants: FC<Props> = observer(({ isEditingEnabled }) => {
  const { calcModelWerStore } = useStore()
  const {
    listOfStationsStore: {
      parametersStore: { plantCascades, removePlant, editMode, updatePlantDate },
    },
  } = calcModelWerStore

  // Функция-костыль для синхронизации рендера таблицы с изменениями в сторе
  const mapPlantData = () =>
    plantCascades?.upstreamPlants?.map((plant) => {
      return {
        tabId: plant.plantId,
        plantName: plant.plantName || null,
        travelTime: null, // Данные для времени добегания пока бек не дает
        departmentName: plant.werDepartments,
        startDate: plant.startDate,
        endDate: plant.endDate ? plant.endDate : null,
        type: 'ADDED', // Костыль, чтобы можно было редактировать startDate
      }
    }) || []

  const initialRows: IUpstreamPlantsRow[] = mapPlantData()

  const [rows, setRows] = useState(initialRows)
  const [isOpenAddNewPlantModal, setIsOpenAddNewPlantModal] = useState(false)

  useEffect(() => {
    setRows(initialRows)
  }, [plantCascades?.upstreamPlants])

  const baseColumns: IColumn<IUpstreamPlantsRow>[] = [
    {
      name: 'plantName',
      title: 'Станция',
      width: 200,
      render: (value: string) => <UpDownStreamTableCell value={value} />,
    },
    {
      name: 'travelTime',
      title: 'tдоб, дни',
      width: 100,
      render: (value: string) => <UpDownStreamTableCell value={value} />,
    },
    {
      name: 'departmentName',
      title: 'ДЦ ВЭР',
      width: 180,
      render: renderWerDepartments,
    },
    {
      name: 'startDate',
      title: 'Дата начала связи',
      width: 150,
      editing: {
        type: 'date',
        enabled: true,
        showErrorMessage: false,
        onAfterChange: (val, row) => {
          updatePlantDate('upstreamPlants', row.tabId, 'startDate', val)
        },
        onValid: (startDate, row) =>
          validatePlantStartDate(startDate, row.endDate ? startOfDay(new Date(row.endDate)) : null),
      },
      getCellRenderValue: (value: IUpstreamPlantsRow['startDate']) => format(new Date(value), 'dd.MM.yyy'),
      render: (value: IUpstreamPlantsRow['startDate'], _, defaultClassName) => (
        <UpDownStreamTableCell value={prepareDate(value)} className={defaultClassName} />
      ),
    },
    {
      name: 'endDate',
      title: 'Дата окончания связи',
      width: 260,
      editing: {
        type: 'date',
        enabled: true,
        showErrorMessage: false,
        onAfterChange: (val, row) => {
          updatePlantDate('upstreamPlants', row.tabId, 'endDate', val)
        },
        onValid: (endDate, row) =>
          validatePlantEndDate(endDate, row.startDate ? startOfDay(new Date(row.startDate)) : null),
      },
      canClearCell: true,
      getCellRenderValue: (value: IUpstreamPlantsRow['endDate']) => (value ? format(new Date(value), 'dd.MM.yyy') : ''),
      render: (value: IUpstreamPlantsRow['endDate'], _, defaultClassName) => (
        <UpDownStreamTableCell value={value ? prepareDate(value) : ''} className={defaultClassName} />
      ),
    },
  ]

  const actionColumn = {
    name: 'action',
    title: '',
    width: 50,
    headRender: () => {
      return (
        <div className={cls.actionHeader}>
          <Tooltip title='Добавить связь'>
            <span>
              <IconButton
                sx={{
                  color: 'var(--primary-color)',
                  display: 'inline-flex!important',
                  padding: 0,
                }}
                className={cls.addIcon}
                onClick={() => {
                  setIsOpenAddNewPlantModal(true)
                }}
                disabled={!isEditingEnabled}
              >
                <Icon name='plus' width={13} />
              </IconButton>
            </span>
          </Tooltip>
        </div>
      )
    },
    render: (_: unknown, row: IUpstreamPlantsRow) => {
      return (
        <div className={cls.actionsWrapper}>
          <div className={cls.iconCell}>
            <Tooltip title='Удалить'>
              <span>
                <IconButton
                  onClick={() => {
                    removePlant('upstreamPlants', row.tabId)
                    setRows(mapPlantData())
                  }}
                  disabled={!isEditingEnabled}
                  className={classNames('', { [cls.iconRedBtn]: isEditingEnabled })}
                >
                  <Icon name='trash' width={13} height={13} />
                </IconButton>
              </span>
            </Tooltip>
          </div>
        </div>
      )
    },
  }

  const columns = (editMode ? [...baseColumns, actionColumn] : baseColumns) as IColumn<IUpstreamPlantsRow>[]

  return (
    <div className={cls.container}>
      <div className={cls.title}>Вышележащая ГЭС</div>
      <TableV1
        setRows={setRows}
        columns={columns}
        rows={rows}
        editMode={isEditingEnabled}
        className={cls.table}
        maxVisibleRows={2}
        rowHeight='auto'
      />
      {isOpenAddNewPlantModal && (
        <AddPlantModal onClose={() => setIsOpenAddNewPlantModal(false)} plantType='upstreamPlants' />
      )}
    </div>
  )
})
