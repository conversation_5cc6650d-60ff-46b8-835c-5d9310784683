.modal > div > div {
  min-width: 678px !important;

  &Footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
  }
  &FooterRight {
    display: flex;
    align-items: center;
    gap: 1em;
  }
}

.wrapper {
  min-width: 650px;
  min-height: 300px;
}

.row {
  display: flex;
  margin: 0.5em 0;
  width: 650px;
}

.labelRow {
  flex-shrink: 0;
  width: 150px;
  font-weight: 600;

  & span {
    font-weight: inherit !important;
  }
}

.valueBlock {
  width: 50%;

  &Item {
    width: 186px;
  }
  &Date {
    display: flex;
    justify-content: start;
  }
  &Comment {
    width: 484px;
  }
}

.select {
  width: 186px;
  & div {
    font-size: 0.75rem !important;
    font-weight: 600;
  }
}

.textFieldInput {
  width: 100%;
}

.numberFieldInput {
  & > div {
    width: 186px;
  }
}
