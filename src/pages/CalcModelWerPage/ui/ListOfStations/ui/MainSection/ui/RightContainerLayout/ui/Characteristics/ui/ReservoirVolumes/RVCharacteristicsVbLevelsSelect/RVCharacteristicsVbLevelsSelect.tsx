import { observer } from 'mobx-react'
import { useEffect } from 'react'
import { Select } from 'shared/ui/Select'
import { useStore } from 'stores/useStore'

import cls from './RVCharacteristicsVbLevelsSelect.module.scss'

export const RVCharacteristicsVbLevelsSelect = observer(() => {
  const {
    calcModelWerStore: {
      isSelectedDateEditable,
      selectedPlant,
      isSelectedPlantViewOnly,
      listOfStationsStore: {
        characteristicsStore: { editMode, reservoirVolumeStore },
      },
    },
  } = useStore()
  const {
    getReservoirVolumeIndicatorVbLevel,
    updateCurrentVbLevelIndicator,
    isEditRows,
    characteristicsDataSpreadsheet,
    currentSettings,
  } = reservoirVolumeStore
  const isSelectDisabled = !editMode || !isSelectedDateEditable || isSelectedPlantViewOnly || isEditRows

  const onSelectIndicator = (e: string) => {
    if (selectedPlant) {
      updateCurrentVbLevelIndicator(e, selectedPlant.plantId)
    }
  }

  useEffect(() => {
    if (selectedPlant) {
      getReservoirVolumeIndicatorVbLevel(selectedPlant.plantId)
    }
  }, [selectedPlant])

  if (!characteristicsDataSpreadsheet || !currentSettings) {
    return null
  }

  return (
    <div className={cls.row}>
      <label htmlFor='vbLevel' className={cls.selectLabel}>
        Показатель уровня ВБ
      </label>
      <Select
        className={cls.select}
        variant='outlined'
        items={reservoirVolumeStore.vbLevelIndicators}
        value={reservoirVolumeStore?.currentVbLevelIndicator?.id}
        onChange={onSelectIndicator}
        id='vbLevel'
        disabled={isSelectDisabled}
      />
    </div>
  )
})
