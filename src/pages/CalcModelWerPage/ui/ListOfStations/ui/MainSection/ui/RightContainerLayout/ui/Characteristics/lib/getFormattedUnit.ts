// Возвращает форматированную единицу измерения
export const getFormattedUnit = (unit: string) => {
  switch (unit) {
    case 'MLN_M3':
      return 'млн.м³'
    case 'KM3':
      return 'км³'
    case 'M3_S_MW':
      return '(м³/с)/МВт'
    case 'M3_S_MLN_KWH':
      return '(м³/с)/млн.кВт⋅ч'
    case 'M3_MW':
      return 'м³/МВт'
    case 'M3_MLN_KWH':
      return 'м³/млн.кВт⋅ч'
    default:
      return unit
  }
}
