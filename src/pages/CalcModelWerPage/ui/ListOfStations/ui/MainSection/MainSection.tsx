import { observer } from 'mobx-react'
import { useActiveTabLogic } from 'pages/CalcModelWerPage/ui/ListOfStations/hooks/useActiveTabLogic'
import { useHotkeys } from 'react-hotkeys-hook'
import { useStore } from 'stores/useStore'

import { RightContainerLayout } from './ui/RightContainerLayout'

export const MainSection = observer(() => {
  const {
    calcModelWerStore: {
      listOfStationsStore: { activeTab },
    },
  } = useStore()
  const { component, onSave, onReset, isModified } = useActiveTabLogic(activeTab.value)

  useHotkeys('ctrl+shift+s', () => isModified && onSave(), {
    enableOnFormTags: true,
  })
  useHotkeys('ctrl+shift+x', () => isModified && onReset(), {
    enableOnFormTags: true,
  })

  return (
    <RightContainerLayout onSave={onSave} onReset={onReset} isModified={isModified}>
      {component}
    </RightContainerLayout>
  )
})
