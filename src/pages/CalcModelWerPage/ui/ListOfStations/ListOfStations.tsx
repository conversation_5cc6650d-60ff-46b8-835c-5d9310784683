import { observer } from 'mobx-react'
import { useActiveTabLogic } from 'pages/CalcModelWerPage/ui/ListOfStations/hooks/useActiveTabLogic'
import { useEffect } from 'react'
import { CheckEditComponent } from 'shared/ui/CheckEditComponent'
import { useStore } from 'stores/useStore'

import cls from './ListOfStations.module.scss'
import { MainSection } from './ui/MainSection'
import { SidePanel } from './ui/SidePanel'

export const ListOfStations = observer(() => {
  const { calcModelWerStore } = useStore()
  const {
    listOfStationsStore: { activeTab, resetStore },
  } = calcModelWerStore
  const { isModified } = useActiveTabLogic(activeTab.value)

  useEffect(() => {
    return () => {
      resetStore()
    }
  }, [])

  return (
    <CheckEditComponent isEdit={isModified}>
      <div className={cls.listOfStations}>
        <SidePanel />
        <MainSection />
      </div>
    </CheckEditComponent>
  )
})
