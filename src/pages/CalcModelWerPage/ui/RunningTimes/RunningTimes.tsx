import { I<PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON> } from '@mui/material'
import {
  ICalcAllRunningTimesParams,
  ICalcModelRunningTimeItem,
  ICalcModelRunningTimeTableRow,
} from 'entities/api/calcModelWerManager.entities'
import { ROLES } from 'entities/shared/roles.entities'
import { observer } from 'mobx-react'
import { useResizeObserver } from 'pages/CalcModelWerPage/hooks/useResizeObserver'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { classNames } from 'shared/lib/classNames'
import { readableISO8601DateMonth } from 'shared/lib/dateFormates'
import { Button } from 'shared/ui/Button'
import { CheckEditComponent } from 'shared/ui/CheckEditComponent'
import { Icon } from 'shared/ui/Icon'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { Switch } from 'shared/ui/Switch'
import { useStore } from 'stores/useStore'
import { IColumn, Table } from 'widgets/Table'

import cls from './RunningTimes.module.scss'
import { AddEditRunningTimeModal } from './ui/AddEditRunningTimeModal'
import { CalcAllRunningTimesModal } from './ui/CalcAllRunningTimesModal'
import { RunningTimesTableCell } from './ui/RunningTimesTableCell'

const columnSearchDisabled = [
  'period',
  'addRunningTime',
  'runningTimeBeginDate',
  'runningTimeStopDate',
  'runningTimeNormTime',
  'runningTimeMasterTime',
  'arrow',
  'runningTimeCalcTime',
  'runningTimeComment',
  'actions',
]

const columnBands = [
  {
    title: 'Время добегания, дни',
    children: [
      { columnName: 'runningTimeNormTime' },
      { columnName: 'runningTimeMasterTime' },
      { columnName: 'arrow' },
      { columnName: 'runningTimeCalcTime' },
    ],
  },
]

// общая высота других элементов на вкладке
const HEIGHT_OF_OTHER_ELEMENTS_ON_PAGE = 138

export const RunningTimes = observer(() => {
  const {
    calcModelWerStore: { runningTimesStore },
    authStore: { userDetail },
  } = useStore()
  const {
    changeMasterRunningTime,
    isDataModified,
    hasExistingRunningTimes,
    warnings,
    resetRunningTimesChanges,
    saveRunningTimes,
    setShowActiveOnly,
    createRunningTime,
    updateRunningTime,
    setSelectedRunningTime,
    deleteRunningTime,
    resetWarnings,
  } = runningTimesStore
  const height = useResizeObserver(HEIGHT_OF_OTHER_ELEMENTS_ON_PAGE)
  const [isCalcAllRunningTimesModalOpen, setIsCalcAllRunningTimesModalOpen] = useState(false)
  const editMode = useMemo(
    () =>
      userDetail.roles
        .map((el) => el.role)
        .some((el: string) => {
          return [ROLES.TECH_ADMIN_CM].some((item) => item === el)
        }),
    [userDetail],
  )

  const checkEnableRowEditing = useCallback(
    (row: ICalcModelRunningTimeTableRow) => row.active && row.editable && editMode,
    [editMode],
  )

  const calcButtonTooltip = useMemo(() => {
    if (!hasExistingRunningTimes) {
      return 'Отсутствуют времена добегания'
    }
    if (isDataModified) {
      return 'Включен режим редактирования таблицы'
    }

    return null
  }, [hasExistingRunningTimes, isDataModified])

  useHotkeys('ctrl+shift+s', () => editMode && isDataModified && saveRunningTimes(), {
    enableOnFormTags: true,
  })
  useHotkeys('ctrl+shift+x', () => editMode && isDataModified && resetRunningTimesChanges(), {
    enableOnFormTags: true,
  })

  const onAddClick = (row: ICalcModelRunningTimeTableRow) => {
    setSelectedRunningTime({ row, type: 'add' })
  }

  const onEditClick = (row: ICalcModelRunningTimeTableRow) => {
    setSelectedRunningTime({ row, type: 'edit' })
  }

  const onSaveRunningTime = (values: ICalcModelRunningTimeItem) => {
    if (runningTimesStore.selectedRunningTime) {
      if (runningTimesStore.selectedRunningTime.isEmptyRow) {
        createRunningTime(runningTimesStore.selectedRunningTime.plantId, values)
      } else {
        updateRunningTime(runningTimesStore.selectedRunningTime.plantId, values)
      }
    }
    setSelectedRunningTime()
  }

  const onDeleteClick = (row: ICalcModelRunningTimeTableRow) => {
    if (row.runningTimeId) {
      deleteRunningTime(row.plantId, row.runningTimeId)
    }
    setSelectedRunningTime()
  }

  const onOpenAllRunningTimesModal = () => {
    setIsCalcAllRunningTimesModalOpen(true)
  }

  const onCloseAllRunningTimesModal = () => {
    setIsCalcAllRunningTimesModalOpen(false)
    resetWarnings()
  }

  const onSubmitCalcAllRunningTimes = async (data: ICalcAllRunningTimesParams) => {
    const res = await runningTimesStore.calcAllRunningTimes(data)
    if (res) {
      onCloseAllRunningTimesModal()
    }
  }

  const columns: IColumn[] = [
    {
      width: 400,
      title: 'Связь',
      name: 'bond',
      getCellRowSpan: (value) => {
        return (value as ICalcModelRunningTimeTableRow).rowSpan
      },
      render: (value, row: ICalcModelRunningTimeTableRow) => <RunningTimesTableCell value={value} row={row} />,
    },
    {
      width: 200,
      title: 'Период действия связи',
      name: 'period',
      getCellRowSpan: (value) => {
        return (value as ICalcModelRunningTimeTableRow).rowSpan
      },
      render: (value, row: ICalcModelRunningTimeTableRow) => <RunningTimesTableCell value={value} row={row} />,
      isBlockedSorting: true,
    },
    {
      width: 40,
      title: '',
      name: 'addRunningTime',
      getCellRowSpan: (value) => {
        return (value as ICalcModelRunningTimeTableRow).rowSpan
      },
      render: (_, row: ICalcModelRunningTimeTableRow) => {
        return (
          <div className={cls.flexContainer}>
            <Tooltip title='Добавить время добегания'>
              <span>
                <IconButton
                  className={classNames('', { [cls.iconBlueBtn]: checkEnableRowEditing(row) })}
                  disabled={!checkEnableRowEditing(row)}
                  onClick={() => onAddClick(row)}
                >
                  <Icon name='plus' width={13} />
                </IconButton>
              </span>
            </Tooltip>
          </div>
        )
      },
      isBlockedSorting: true,
    },
    {
      width: 100,
      title: 'Дата начала',
      name: 'runningTimeBeginDate',
      render: (value, row: ICalcModelRunningTimeTableRow) => (
        <RunningTimesTableCell value={value ? readableISO8601DateMonth(value) : value} row={row} />
      ),
      isBlockedSorting: true,
    },
    {
      width: 100,
      title: 'Дата конца',
      name: 'runningTimeStopDate',
      render: (value, row: ICalcModelRunningTimeTableRow) => (
        <RunningTimesTableCell value={value ? readableISO8601DateMonth(value) : value} row={row} />
      ),
      isBlockedSorting: true,
    },
    {
      width: 60,
      title: 'Норм.',
      name: 'runningTimeNormTime',
      render: (value, row: ICalcModelRunningTimeTableRow) => <RunningTimesTableCell value={value} row={row} />,
      isBlockedSorting: true,
    },
    {
      width: 90,
      title: 'Основное',
      name: 'runningTimeMasterTime',
      render: (value, row: ICalcModelRunningTimeTableRow) => <RunningTimesTableCell value={value} row={row} />,
      isBlockedSorting: true,
    },
    {
      width: 50,
      title: '',
      name: 'arrow',
      render: (_, row: ICalcModelRunningTimeTableRow) => {
        if (row?.isEmptyRow) return <></>

        return (
          <div className={cls.flexContainer}>
            <IconButton
              disabled={!checkEnableRowEditing(row)}
              className={classNames('', {
                [cls.iconBlueBtn]: checkEnableRowEditing(row),
                [cls.iconArrowBtn]: !row.enableShiftTimeButton && checkEnableRowEditing(row),
              })}
              onClick={() => changeMasterRunningTime({ plantId: row.plantId, runningTimeId: row.id })}
            >
              <Icon name='bigArrowLeft' width={16} />
            </IconButton>
          </div>
        )
      },
      isBlockedSorting: true,
      headRender: () => {
        const enableShiftTimeButton = runningTimesStore.runningTimes.some(
          (el) => el.enableShiftTimeButton && checkEnableRowEditing(el),
        )

        return (
          <div className={cls.flexContainer}>
            <IconButton
              className={classNames(cls.iconBlueBtn, { [cls.iconArrowBtn]: !enableShiftTimeButton })}
              disabled={!enableShiftTimeButton}
              onClick={() => changeMasterRunningTime()}
            >
              <Icon name='bigArrowLeft' width={16} />
            </IconButton>
          </div>
        )
      },
    },
    {
      width: 60,
      title: 'Расч.',
      name: 'runningTimeCalcTime',
      render: (value, row: ICalcModelRunningTimeTableRow) => <RunningTimesTableCell value={value} row={row} />,
      isBlockedSorting: true,
    },
    {
      width: 300,
      title: 'Комментарий',
      name: 'runningTimeComment',
      render: (value, row: ICalcModelRunningTimeTableRow) => <RunningTimesTableCell value={value} row={row} />,
      isBlockedSorting: true,
    },
    {
      width: 80,
      title: '',
      name: 'actions',
      render: (_, row: ICalcModelRunningTimeTableRow) => {
        if (row?.isEmptyRow) return <></>

        return (
          <div className={cls.actions}>
            <Tooltip title='Настроить время добегания'>
              <span>
                <IconButton
                  onClick={() => onEditClick(row)}
                  disabled={!checkEnableRowEditing(row)}
                  className={classNames('', { [cls.iconBlueBtn]: checkEnableRowEditing(row) })}
                >
                  <Icon name='settings' width={13} height={13} />
                </IconButton>
              </span>
            </Tooltip>

            <Tooltip title='Удалить время добегания'>
              <span>
                <IconButton
                  onClick={() => onDeleteClick(row)}
                  disabled={!checkEnableRowEditing(row)}
                  className={classNames('', { [cls.iconRedBtn]: checkEnableRowEditing(row) })}
                >
                  <Icon name='trash' width={13} height={13} />
                </IconButton>
              </span>
            </Tooltip>
          </div>
        )
      },
      isBlockedSorting: true,
    },
  ]

  useEffect(() => {
    runningTimesStore.getRunningTimes()

    return () => {
      runningTimesStore.resetStore()
    }
  }, [])

  return (
    <CheckEditComponent isEdit={isDataModified}>
      <div className={cls.container}>
        <SubtitleWithActions
          title={
            <div className={cls.headerActions}>
              <h2>Расчёт времени добегания</h2>
              <Tooltip title={calcButtonTooltip}>
                <span>
                  <Button
                    className={cls.calcBtn}
                    onClick={onOpenAllRunningTimesModal}
                    disabled={runningTimesStore.isLoading || !editMode || !hasExistingRunningTimes || isDataModified}
                  >
                    Рассчитать
                  </Button>
                </span>
              </Tooltip>

              <Switch
                onChange={(_, checked: boolean) => {
                  setShowActiveOnly(checked)
                }}
                disabled={runningTimesStore.isLoading}
                checked={runningTimesStore.showActiveOnly}
                label='Только действующие'
              />
            </div>
          }
          actions={[
            <Button
              key='reset'
              disabled={!editMode || !isDataModified}
              variant='outlined'
              onClick={resetRunningTimesChanges}
            >
              Сбросить
            </Button>,
            <Button key='save' disabled={!editMode || !isDataModified} onClick={saveRunningTimes}>
              Сохранить
            </Button>,
          ]}
          isActionsVisible
        />

        <div className={cls.table}>
          <Table
            loading={runningTimesStore.isLoading}
            rows={runningTimesStore.runningTimes}
            columns={columns}
            height={height}
            editMode={false}
            columnBands={columnBands}
            columnSearchDisabled={columnSearchDisabled}
          />
        </div>
      </div>

      {!!runningTimesStore.selectedRunningTime && (
        <AddEditRunningTimeModal
          row={runningTimesStore.selectedRunningTime}
          onClose={setSelectedRunningTime}
          onSave={onSaveRunningTime}
        />
      )}

      {isCalcAllRunningTimesModalOpen && (
        <CalcAllRunningTimesModal
          onSubmit={onSubmitCalcAllRunningTimes}
          onClose={onCloseAllRunningTimesModal}
          warnings={warnings}
        />
      )}
    </CheckEditComponent>
  )
})
