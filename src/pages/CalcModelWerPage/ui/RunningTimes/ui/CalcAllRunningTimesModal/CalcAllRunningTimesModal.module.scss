.modal {
  &Content {
    display: flex;
    flex-direction: column;
  }
}

.titleRow {
  display: flex;
  justify-content: space-between;

  &<PERSON><PERSON> {
    padding: 0;
  }

  &Icon {
    width: 30px !important;
    height: 30px !important;
    stroke-width: 0.5px;

    &Active {
      stroke: var(--primary-color);
      color: var(--primary-color);
    }
  }
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.loadingButton {
  height: 26px;
  width: 90px;
}

.protocolButton {
  border-color: var(--red-color) !important;
  color: var(--text-color) !important;
}
