.titleRow {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &<PERSON><PERSON> {
    padding: 0;
  }

  &Icon {
    width: 30px !important;
    height: 30px !important;
    stroke-width: 0.5px;

    &Active {
      stroke: var(--primary-color);
      color: var(--primary-color);
    }
  }
}

.modal {
  --left-side-width: 440px;
  --right-side-width: 600px;
  --short-input-width: 90px;
  display: flex;
  flex-direction: column;
  height: 100%;

  &Title {
    color: var(--text-gray);
    margin-bottom: 1.5rem;
  }

  &Content {
    display: flex;
  }

  .leftSide {
    max-width: var(--left-side-width);
  }

  .rightSide {
    min-width: var(--right-side-width);
  }

  .divider {
    width: 1px;
    background: var(--gray-background);
    margin: 0 1rem;
  }

  .actions {
    width: var(--left-side-width);
    display: flex;
    margin-top: 1.5rem;
    justify-content: flex-end;
  }
}

.formValues {
  display: flex;
  gap: 1rem;
  flex-direction: column;
}

.inputRow {
  display: flex;

  &Label {
    width: 160px;
    font-weight: 600;
    cursor: default;
  }
}

.comment {
  width: 250px;
  margin-bottom: 1rem;
}

.datePicker {
  width: 100px;
}

.days {
  width: 80px;
}

.calcPeriod {
  height: auto;
  min-height: 150px;
}

.availableRange {
  display: flex;

  &Inputs {
    flex: 0 0 65%;
    display: flex;

    &Label {
      padding-right: 2rem;
    }

    &Item {
      width: var(--short-input-width);

      &First {
        padding-right: 0.5rem;
      }
    }
  }

  .calcSettings {
    flex: 0 0 35%;
    display: flex;
    gap: 0.5rem;
    flex-direction: column;
  }
}

.shortFieldRow {
  display: flex;
  align-items: center;

  &Label {
    width: 140px;
  }

  &Input {
    width: var(--short-input-width);
  }
}

.calcBtn {
  background-color: var(--green-color) !important;
  height: 26px;
  width: 94px;
}

.calcResult {
  padding-top: 1rem;
}

.label {
  font-weight: 600;
}
