import CloseIcon from '@mui/icons-material/Close'
import { IconButton, Radio, RadioGroup } from '@mui/material'
import { format } from 'date-fns'
import {
  IAddEditRunningTimeCalcForm,
  IAddEditRunningTimeCalcFormValidationKeys,
  IAddEditRunningTimeForm,
  ICalcModelRunningTimeItem,
  ICalcModelRunningTimeTableRow,
  ICalcSingleRunningTimeParams,
  ICalcSingleRunningTimeResultDto,
} from 'entities/api/calcModelWerManager.entities'
import { DATE_FORMATS } from 'entities/constants'
import { IEnrichedTabIdData } from 'entities/shared/common.entities'
import { observer } from 'mobx-react'
import { FC, useEffect, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { parseISO8601DateMonth } from 'shared/lib/dateFormates'
import { But<PERSON> } from 'shared/ui/Button'
import { DatePicker } from 'shared/ui/DatePicker'
import { DataPickerValue } from 'shared/ui/DateRangePicker/ui/DateRangePicker'
import { ErrorExplanationIcon } from 'shared/ui/ErrorExplanationIcon'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Modal } from 'shared/ui/Modal'
import { TextField } from 'shared/ui/TextField'
import { NumberOption } from 'shared/ui/TextField/model/types'
import { useStore } from 'stores/useStore'
import { IColumn, Table } from 'widgets/Table'

import { initDateValues, TDateType } from '../../lib/constants'
import { CalcPeriodSettings } from '../CalcPeriodSettings'
import cls from './AddEditRunningTimeModal.module.scss'
import { MAX_COMMENT_LENGTH, validateCalcForm, validateForm } from './lib/validation'

interface Props {
  onClose: () => void
  row: ICalcModelRunningTimeTableRow
  onSave: (values: ICalcModelRunningTimeItem) => void
}

type TCalcFormValues = boolean | null | undefined | string | number | number[] | DataPickerValue
type ICalculatedValues = {
  min: number | null
  average: number | null
  max: number | null
}

type TCalculatedValuesKeys = keyof ICalculatedValues

const numberOption: NumberOption = {
  lengthAfterComma: 2,
  min: 0,
  max: 100,
}

const availableRangeNumberOptions: NumberOption = {
  isInteger: true,
  lengthBeforeComma: 3,
  max: 100,
  min: 0,
  positive: true,
}

const INIT_MIN_VALUE = 0
const INIT_MAX_VALUE = 14
const INIT_GRID_STEP = 0.5
const INIT_ACCURACY = 0.01

const createForm = (row?: ICalcModelRunningTimeTableRow): IAddEditRunningTimeForm => {
  return {
    runningTimeBeginDate: row?.runningTimeBeginDate ? parseISO8601DateMonth(row.runningTimeBeginDate) : null,
    runningTimeStopDate: row?.runningTimeStopDate ? parseISO8601DateMonth(row.runningTimeStopDate) : null,
    runningTimeNormTime: row?.runningTimeNormTime ?? null,
    runningTimeMasterTime: row?.runningTimeMasterTime ?? null,
    runningTimeCalcTime: row?.runningTimeCalcTime ?? null,
    runningTimeComment: row?.runningTimeComment ?? null,
  }
}

const createCalcForm = (row?: ICalcModelRunningTimeTableRow): IAddEditRunningTimeCalcForm => ({
  dateRange: [null, null],
  checkboxDateValues: { days: false, months: false, years: false },
  dateValues: { ...initDateValues },
  availableRangeMinValue: row?.runningTimeMinTime ?? INIT_MIN_VALUE,
  availableRangeMaxValue: row?.runningTimeMaxTime ?? INIT_MAX_VALUE,
  calcStep: row?.runningTimeGridStep ?? INIT_GRID_STEP,
  precision: row?.runningTimeAccuracy ?? INIT_ACCURACY,
})

export const AddEditRunningTimeModal: FC<Props> = observer(({ onClose, row, onSave }) => {
  const {
    calcModelWerStore: { runningTimesStore },
  } = useStore()
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [form, setForm] = useState<IAddEditRunningTimeForm>(() => createForm())
  const [calcForm, setCalcForm] = useState<IAddEditRunningTimeCalcForm>(() => createCalcForm())
  const [calcFormErrors, setCalcFormErrors] = useState<Record<string, string>>({})
  const [calcResultRows, setCalcResultRows] = useState<IEnrichedTabIdData<ICalcSingleRunningTimeResultDto>[]>([])
  const [tableSelection, setTableSelection] = useState<string[]>([])
  const [calcCheckboxValue, setCalcCheckboxValue] = useState<TCalculatedValuesKeys>('average')
  const [calculatedValues, setCalculatedValues] = useState<ICalculatedValues>(() => ({
    min: null,
    max: null,
    average: null,
  }))
  const [isCalcLoading, setIsCalcLoading] = useState(false)

  const onMainSettingsChange = (value: string | number | Date | null, key: keyof IAddEditRunningTimeForm) => {
    const newForm = { ...form, [key]: value }
    if (key === 'runningTimeBeginDate' || key === 'runningTimeStopDate') {
      const [_, newErrors] = validateForm(newForm, formErrors, key)
      setFormErrors(newErrors)
    }
    setForm(newForm)
  }

  const handleBlurMainSettingField = (key: string) => {
    const [_, newErrors] = validateForm(form, formErrors, key)
    setFormErrors(newErrors)
  }

  const handleSaveMainSettings = () => {
    const [isValid, newErrors] = validateForm(form, formErrors)
    setFormErrors(newErrors)
    if (!isValid) return

    const data: ICalcModelRunningTimeItem = {
      beginDate: format(form.runningTimeBeginDate as Date, DATE_FORMATS['--MM-dd']),
      stopDate: format(form.runningTimeStopDate as Date, DATE_FORMATS['--MM-dd']),
      calculatedTime: form.runningTimeCalcTime,
      masterTime: Number(form.runningTimeMasterTime),
      normTime: Number(form.runningTimeNormTime),
      comment: form.runningTimeComment ?? undefined,
      id: row?.isEmptyRow ? Date.now() : row.id,
      accuracy: calcForm.precision ? Number(calcForm.precision) : null,
      gridStep: calcForm.calcStep ? Number(calcForm.calcStep) : null,
      maxTime: calcForm.availableRangeMaxValue ? Number(calcForm.availableRangeMaxValue) : null,
      minTime: calcForm.availableRangeMinValue ? Number(calcForm.availableRangeMinValue) : null,
    }

    onSave(data)
  }

  const onChangeTableSelection = (ids: string[], rows: IEnrichedTabIdData<ICalcSingleRunningTimeResultDto>[]) => {
    const filtered = rows.filter((el) => ids.includes(el.tabId as string))

    const values = filtered.map((el) => el.runningTime)
    const max = filtered.length ? Math.max(...values) : null
    const min = filtered.length ? Math.min(...values) : null
    const average = filtered.length
      ? Math.round((values.reduce((acc, cur) => acc + cur, 0) / values.length) * 100) / 100
      : null
    setTableSelection(ids)
    setCalculatedValues({ average, min, max })
  }

  const onCalcSettingsChange = (
    value: TCalcFormValues,
    key: keyof IAddEditRunningTimeCalcForm,
    dateTypeKey?: TDateType,
  ) => {
    let newCalcForm = { ...calcForm }
    if (['precision', 'calcStep', 'availableRangeMinValue', 'availableRangeMaxValue'].includes(key)) {
      newCalcForm = { ...calcForm, [key]: value }
    } else if (key === 'dateRange' && Array.isArray(value)) {
      newCalcForm = { ...calcForm, dateRange: value as DataPickerValue }
      const [_, newCalcFormErrors] = validateCalcForm(newCalcForm, calcFormErrors, 'dateRange')
      setCalcFormErrors(newCalcFormErrors)
    } else if (key === 'checkboxDateValues' && dateTypeKey && typeof value === 'boolean') {
      const checkboxDateValues = { ...calcForm.checkboxDateValues, [dateTypeKey]: value }
      newCalcForm = { ...calcForm, checkboxDateValues }
    } else if (key === 'dateValues' && dateTypeKey && Array.isArray(value)) {
      const dateValues = { ...calcForm.dateValues, [dateTypeKey]: value.sort((a, b) => Number(a) - Number(b)) }
      newCalcForm = { ...calcForm, dateValues }
    }

    setCalcForm(newCalcForm)
  }

  const onBlurCalcSetting = (key: IAddEditRunningTimeCalcFormValidationKeys) => {
    if (calcFormErrors[key]) {
      const [_, newCalcFormErrors] = validateCalcForm(calcForm, calcFormErrors, key)
      setCalcFormErrors(newCalcFormErrors)
    }
  }

  const onCalcResultClick = async () => {
    const [isCalcFormValid, newCalcFormErrors] = validateCalcForm(calcForm, calcFormErrors)
    const [isFormValid, newFormErrors] = validateForm(form, formErrors, ['runningTimeStopDate', 'runningTimeBeginDate'])
    setCalcFormErrors(newCalcFormErrors)
    setFormErrors(newFormErrors)
    if (!isCalcFormValid || !isFormValid) return
    try {
      setIsCalcLoading(true)
      const data: ICalcSingleRunningTimeParams = {
        beginDate: format(form.runningTimeBeginDate as Date, DATE_FORMATS['--MM-dd']),
        stopDate: format(form.runningTimeStopDate as Date, DATE_FORMATS['--MM-dd']),
        days: calcForm.checkboxDateValues.days ? calcForm.dateValues.days : [],
        months: calcForm.checkboxDateValues.months ? calcForm.dateValues.months : [],
        years: calcForm.checkboxDateValues.years ? calcForm.dateValues.years : [],
        periodBegin: format(calcForm.dateRange[0] as Date, DATE_FORMATS.yyyyMMdd),
        periodEnd: format(calcForm.dateRange[1] as Date, DATE_FORMATS.yyyyMMdd),
        plantStreamId: row.plantId,
        accuracy: Number(calcForm.precision),
        gridStep: Number(calcForm.calcStep),
        maxTime: Number(calcForm.availableRangeMaxValue),
        minTime: Number(calcForm.availableRangeMinValue),
      }

      const res = await runningTimesStore.calcSingleRunningTime(data)
      setCalcResultRows(res)
      onChangeTableSelection(
        res.map((el) => el.tabId as string),
        res,
      )
    } catch (error) {
      console.log(error)
    } finally {
      setIsCalcLoading(false)
    }
  }

  const columns: IColumn[] = [
    {
      name: 'year',
      title: 'Расчетный год',
      width: 120,
    },
    {
      name: 'runningTime',
      title: 't,дни',
      width: 100,
    },
    {
      name: 'discrepancy',
      title: 'Средняя невязка,м³/с',
      width: 170,
    },
  ]

  useEffect(() => {
    /** расчетное время меняется в зависимости от изменений в табличной части */
    if (calcResultRows.length) {
      setForm((prev) => ({ ...prev, runningTimeCalcTime: calculatedValues[calcCheckboxValue] }))
    }
  }, [calculatedValues, calcCheckboxValue])

  useEffect(() => {
    setForm(createForm(row))
    setCalcForm(createCalcForm(row))
  }, [row])

  return (
    <Modal
      open
      title={
        <div className={cls.titleRow}>
          <span>Расчёт времени добегания</span>
          <IconButton onClick={onClose} className={cls.titleRowButton} disabled={isCalcLoading}>
            <CloseIcon className={classNames(cls.titleRowIcon, { [cls.titleRowIconActive]: !isCalcLoading })} />
          </IconButton>
        </div>
      }
      actions={
        <div key='actions' className={cls.actions}>
          <Button onClick={handleSaveMainSettings}>{row?.isEmptyRow ? 'Добавить' : 'Применить'}</Button>
        </div>
      }
      maxWidth='xl'
    >
      <div className={cls.modal}>
        <h3 className={cls.modalTitle}>{row.bond}</h3>

        <div className={cls.modalContent}>
          <div className={cls.leftSide}>
            <div className={cls.formValues}>
              <div className={cls.inputRow}>
                <div className={cls.inputRowLabel}>Дата начала</div>
                <DatePicker
                  value={form?.runningTimeBeginDate as Date}
                  views={['day', 'month']}
                  setValue={(value) => onMainSettingsChange(value, 'runningTimeBeginDate')}
                  format={DATE_FORMATS.ddMM}
                  className={cls.datePicker}
                  error={formErrors?.runningTimeBeginDate}
                  showErrorMsg={false}
                />
                <ErrorExplanationIcon title={formErrors?.runningTimeBeginDate} />
              </div>

              <div className={cls.inputRow}>
                <div className={cls.inputRowLabel}>Дата окончания</div>
                <DatePicker
                  value={form?.runningTimeStopDate as Date}
                  views={['day', 'month']}
                  setValue={(value) => onMainSettingsChange(value, 'runningTimeStopDate')}
                  format={DATE_FORMATS.ddMM}
                  className={cls.datePicker}
                  error={formErrors?.runningTimeStopDate}
                  showErrorMsg={false}
                />
                <ErrorExplanationIcon title={formErrors?.runningTimeStopDate} />
              </div>

              <div className={cls.inputRow}>
                <label className={cls.inputRowLabel} htmlFor='comment'>
                  Комментарий
                </label>
                <TextField
                  rows={3}
                  onChange={({ target: { value } }) => onMainSettingsChange(value, 'runningTimeComment')}
                  id='comment'
                  multiline
                  className={cls.comment}
                  maxLength={MAX_COMMENT_LENGTH}
                  value={form.runningTimeComment ?? ''}
                />
                <ErrorExplanationIcon title={formErrors?.runningTimeComment} />
              </div>

              <div className={cls.inputRow}>
                <label className={cls.inputRowLabel} htmlFor='normTime'>
                  Нормативное время
                </label>
                <TextField
                  label='дни'
                  type='number'
                  numberOption={numberOption}
                  onChange={({ target: { value } }) => onMainSettingsChange(value, 'runningTimeNormTime')}
                  id='normTime'
                  value={form.runningTimeNormTime ?? ''}
                  onBlur={() => handleBlurMainSettingField('runningTimeNormTime')}
                  className={cls.days}
                  error={!!formErrors?.runningTimeNormTime}
                />
                <ErrorExplanationIcon title={formErrors?.runningTimeNormTime} />
              </div>

              <div className={cls.inputRow}>
                <label className={cls.inputRowLabel} htmlFor='calcTime'>
                  Расчетное время
                </label>
                <TextField
                  label='дни'
                  id='calcTime'
                  value={form.runningTimeCalcTime ?? ''}
                  disabled
                  type='number'
                  className={cls.days}
                  numberOption={numberOption}
                />
              </div>

              <div className={cls.inputRow}>
                <label className={cls.inputRowLabel} htmlFor='masterTime'>
                  Основное время
                </label>
                <TextField
                  label='дни'
                  variant='outlined'
                  onChange={({ target: { value } }) => onMainSettingsChange(value, 'runningTimeMasterTime')}
                  id='masterTime'
                  value={form.runningTimeMasterTime ?? ''}
                  type='number'
                  numberOption={numberOption}
                  onBlur={() => handleBlurMainSettingField('runningTimeMasterTime')}
                  error={!!formErrors?.runningTimeMasterTime}
                  className={cls.days}
                />
                <ErrorExplanationIcon title={formErrors?.runningTimeMasterTime} />
              </div>
            </div>
          </div>

          <div className={cls.divider} />

          <div className={cls.rightSide}>
            <CalcPeriodSettings
              checkboxValues={calcForm.checkboxDateValues}
              dateRange={calcForm.dateRange}
              dateValues={calcForm.dateValues}
              handleChangeDate={(value) => onCalcSettingsChange(value, 'dateRange')}
              onCheckboxChange={(key, value) => onCalcSettingsChange(value, 'checkboxDateValues', key)}
              onChangeDateValues={(key, value) => onCalcSettingsChange(value, 'dateValues', key)}
              className={cls.calcPeriod}
              error={calcFormErrors?.dateRange}
            />

            <div className={cls.availableRange}>
              <div className={cls.availableRangeInputs}>
                <label htmlFor='minRange' className={classNames(cls.availableRangeInputsLabel, {}, [cls.label])}>
                  Допустимый диапазон
                </label>
                <TextField
                  id='minRange'
                  value={calcForm.availableRangeMinValue ?? ''}
                  onChange={({ target: { value } }) => onCalcSettingsChange(value, 'availableRangeMinValue')}
                  label='минимум'
                  type='number'
                  numberOption={availableRangeNumberOptions}
                  error={!!calcFormErrors?.availableRange}
                  className={classNames(cls.availableRangeInputsItem, {}, [cls.availableRangeInputsItemFirst])}
                  onBlur={() => onBlurCalcSetting('availableRange')}
                />

                <TextField
                  value={calcForm.availableRangeMaxValue ?? ''}
                  onChange={({ target: { value } }) => onCalcSettingsChange(value, 'availableRangeMaxValue')}
                  className={cls.availableRangeInputsItem}
                  label='максимум'
                  type='number'
                  numberOption={availableRangeNumberOptions}
                  error={!!calcFormErrors?.availableRange}
                  onBlur={() => onBlurCalcSetting('availableRange')}
                />
                <ErrorExplanationIcon title={calcFormErrors?.availableRange} />
              </div>

              <div className={cls.calcSettings}>
                <div className={cls.shortFieldRow}>
                  <label htmlFor='step' className={classNames(cls.label, {}, [cls.shortFieldRowLabel])}>
                    Расчетный шаг
                  </label>
                  <TextField
                    id='step'
                    onChange={({ target: { value } }) => onCalcSettingsChange(value, 'calcStep')}
                    label='дни'
                    className={cls.shortFieldRowInput}
                    value={calcForm.calcStep ?? ''}
                    type='number'
                    numberOption={{
                      max: 100,
                      lengthBeforeComma: 3,
                      lengthAfterComma: 2,
                      positive: true,
                      isInteger: false,
                    }}
                    error={!!calcFormErrors?.calcStep}
                    onBlur={() => onBlurCalcSetting('calcStep')}
                  />
                  <ErrorExplanationIcon title={calcFormErrors?.calcStep} />
                </div>

                <div className={cls.shortFieldRow}>
                  <label htmlFor='precision' className={classNames(cls.label, {}, [cls.shortFieldRowLabel])}>
                    Точность
                  </label>
                  <TextField
                    id='precision'
                    onChange={({ target: { value } }) => onCalcSettingsChange(value, 'precision')}
                    label='дни'
                    className={cls.shortFieldRowInput}
                    type='number'
                    value={calcForm.precision ?? ''}
                    numberOption={{
                      min: 0,
                      max: 1,
                      lengthAfterComma: 2,
                      positive: true,
                    }}
                    error={!!calcFormErrors?.precision}
                    onBlur={() => onBlurCalcSetting('precision')}
                  />
                  <ErrorExplanationIcon title={calcFormErrors?.precision} />
                </div>
              </div>
            </div>

            <div>
              <LoadingButton
                loading={isCalcLoading}
                variant='contained'
                className={cls.calcBtn}
                onClick={onCalcResultClick}
              >
                Рассчитать
              </LoadingButton>
            </div>

            {!!calcResultRows.length && (
              <div className={cls.calcResult}>
                <RadioGroup
                  row
                  value={calcCheckboxValue}
                  onChange={(e) => setCalcCheckboxValue(e.target.value as TCalculatedValuesKeys)}
                >
                  <div className={cls.shortFieldRow}>
                    <Radio value='min' />
                    <TextField
                      className={cls.shortFieldRowInput}
                      value={calculatedValues.min ?? ''}
                      label='минимум'
                      disabled
                    />
                  </div>

                  <div className={cls.shortFieldRow}>
                    <Radio value='average' />
                    <TextField
                      className={cls.shortFieldRowInput}
                      value={calculatedValues.average ?? ''}
                      label='среднее'
                      disabled
                    />
                  </div>

                  <div className={cls.shortFieldRow}>
                    <Radio value='max' />
                    <TextField
                      className={cls.shortFieldRowInput}
                      value={calculatedValues.max ?? ''}
                      label='максимум'
                      disabled
                    />
                  </div>
                </RadioGroup>

                <Table
                  rows={calcResultRows}
                  columnSearchDisabled={['year', 'runningTime', 'discrepancy']}
                  showSearchControls={false}
                  columns={columns}
                  selectMode='many'
                  selection={tableSelection}
                  showSelectAll
                  setSelection={(ids) => onChangeTableSelection(ids, calcResultRows)}
                  showSortingControls={false}
                  height={150}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </Modal>
  )
})
