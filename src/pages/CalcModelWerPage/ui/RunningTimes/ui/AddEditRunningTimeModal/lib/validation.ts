import { format } from 'date-fns'
import { IAddEditRunningTimeCalcForm, IAddEditRunningTimeForm } from 'entities/api/calcModelWerManager.entities'

type IErrorObject = Record<string, string>
type IFormValidator = (allValues: IAddEditRunningTimeForm, errors: IErrorObject) => IErrorObject
type ICalcFormValidator = (allValues: IAddEditRunningTimeCalcForm, errors: IErrorObject) => IErrorObject
type IValidationResult = [boolean, IErrorObject]

export const MAX_COMMENT_LENGTH = 300

const formFieldsValidation: Record<string, IFormValidator> = {
  runningTimeComment: (values, errors) => {
    if (errors['runningTimeComment']) delete errors.runningTimeComment
    if (values?.runningTimeComment && values.runningTimeComment?.length > MAX_COMMENT_LENGTH) {
      errors['runningTimeComment'] = `Максимальная длина комментария ${MAX_COMMENT_LENGTH} символов`
    }

    return errors
  },
  runningTimeBeginDate: (values, errors) => {
    if (errors['runningTimeBeginDate']) delete errors.runningTimeBeginDate

    if (!values?.runningTimeBeginDate) {
      errors['runningTimeBeginDate'] = 'Дата начала должна быть установлена для времени добегания'
    } else if (values?.runningTimeBeginDate && values?.runningTimeStopDate) {
      if (format(values.runningTimeBeginDate, '--MM-dd') === format(values.runningTimeStopDate, '--MM-dd')) {
        errors['runningTimeBeginDate'] = 'Даты начала и окончания времени добегания должны отличаться'
        errors['runningTimeStopDate'] = 'Даты начала и окончания времени добегания должны отличаться'
      } else {
        if (errors['runningTimeBeginDate']) delete errors.runningTimeBeginDate
        if (errors['runningTimeStopDate']) delete errors.runningTimeStopDate
      }
    }

    return errors
  },
  runningTimeStopDate: (values, errors) => {
    if (errors['runningTimeStopDate']) delete errors.runningTimeStopDate

    if (!values?.runningTimeStopDate) {
      errors['runningTimeStopDate'] = 'Дата окончания должна быть установлена для времени добегания'
    } else if (values?.runningTimeBeginDate && values?.runningTimeStopDate) {
      if (format(values.runningTimeBeginDate, '--MM-dd') === format(values.runningTimeStopDate, '--MM-dd')) {
        errors['runningTimeBeginDate'] = 'Даты начала и окончания времени добегания должны отличаться'
        errors['runningTimeStopDate'] = 'Даты начала и окончания времени добегания должны отличаться'
      } else {
        if (errors['runningTimeBeginDate']) delete errors.runningTimeBeginDate
        if (errors['runningTimeStopDate']) delete errors.runningTimeStopDate
      }
    }

    return errors
  },
  runningTimeNormTime: (values, errors) => {
    if (errors['runningTimeNormTime']) delete errors.runningTimeNormTime
    if (!values?.runningTimeNormTime) {
      errors['runningTimeNormTime'] = 'Нормативное время должно быть установлено для времени добегания'
    }

    return errors
  },
  runningTimeMasterTime: (values, errors) => {
    if (errors['runningTimeMasterTime']) delete errors.runningTimeMasterTime
    if (!values?.runningTimeMasterTime) {
      errors['runningTimeMasterTime'] = 'Основное время должно быть установлено для времени добегания'
    }

    return errors
  },
}

const checkFilledValue = (value?: number | string | null) => {
  if (value === null || value === undefined || value === '') return false

  return true
}

const calcFormFieldsValidation: Record<string, ICalcFormValidator> = {
  dateRange: (values, errors) => {
    if (errors['dateRange']) delete errors.dateRange

    if (!values.dateRange[0] || !values.dateRange[1]) {
      errors['dateRange'] = 'Расчетный период должен быть установлен для расчета времени добегания'
    } else if (values.dateRange[0] > values.dateRange[1]) {
      errors['dateRange'] =
        'Дата начала расчетного периода должна быть меньше или равна дате конца расчетного периода для расчета времени добегания'
    }

    return errors
  },
  precision: (values, errors) => {
    if (errors['precision']) delete errors.precision

    if (!checkFilledValue(values.precision) || Number(values.precision) < 0.01) {
      errors['precision'] = 'Точность должна быть установлена для расчета времени добегания'
    }

    return errors
  },
  calcStep: (values, errors) => {
    if (errors['calcStep']) delete errors.calcStep

    if (values.calcStep === null || values.calcStep === undefined || values.calcStep === '') {
      errors['calcStep'] = 'Расчетный шаг должен быть установлен для расчета времени добегания'
    }

    return errors
  },
  availableRange: (values, errors) => {
    if (errors['availableRange']) delete errors.availableRange

    const { availableRangeMaxValue, availableRangeMinValue } = values
    if (!checkFilledValue(values.availableRangeMinValue) || !checkFilledValue(values.availableRangeMaxValue)) {
      errors['availableRange'] =
        'Поля максимальный и минимальный допустимый диапазон должны быть заполнены для расчета времени добегания'
    } else if (checkFilledValue(values.availableRangeMinValue) && checkFilledValue(values.availableRangeMaxValue)) {
      if (Number(availableRangeMinValue) >= Number(availableRangeMaxValue)) {
        errors['availableRange'] =
          'Максимальный допустимый диапазон должен быть больше минимального для расчета времени добегания'
      }
    }

    return errors
  },
}

export const validateForm = (
  values: IAddEditRunningTimeForm,
  errors: IErrorObject,
  keys?: string | string[],
): IValidationResult => {
  let newErrorObject = { ...errors }

  let allValidationKeys: string[] = []
  if (keys) {
    if (Array.isArray(keys)) {
      allValidationKeys = keys
    } else {
      allValidationKeys = [keys]
    }
  } else {
    allValidationKeys = Object.keys(formFieldsValidation)
  }

  allValidationKeys.forEach((k) => {
    newErrorObject = formFieldsValidation[k](values, newErrorObject)
  })

  return [!Object.keys(newErrorObject).length, newErrorObject]
}

export const validateCalcForm = (
  values: IAddEditRunningTimeCalcForm,
  errors: IErrorObject,
  keys?: string | string[],
): IValidationResult => {
  let newErrorObject = { ...errors }

  let allValidationKeys: string[] = []
  if (keys) {
    if (Array.isArray(keys)) {
      allValidationKeys = keys
    } else {
      allValidationKeys = [keys]
    }
  } else {
    allValidationKeys = ['availableRange', 'precision', 'calcStep', 'dateRange']
  }

  allValidationKeys.forEach((k) => {
    newErrorObject = calcFormFieldsValidation[k](values, newErrorObject)
  })

  return [!Object.keys(newErrorObject).length, newErrorObject]
}
