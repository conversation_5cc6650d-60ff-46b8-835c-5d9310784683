import { Checkbox } from '@mui/material'
import { format } from 'date-fns'
import ru from 'date-fns/locale/ru'
import { FC } from 'react'
import { classNames } from 'shared/lib/classNames'
import { DataPickerValue, DateRangePicker } from 'shared/ui/DateRangePicker/ui/DateRangePicker'
import { ErrorExplanationIcon } from 'shared/ui/ErrorExplanationIcon'
import { ItemsProps, Select } from 'shared/ui/Select/Select'

import { TDateType } from '../../lib/constants'
import cls from './CalcPeriodSettings.module.scss'

interface IFormValue {
  id: TDateType
  label: string
  items: ItemsProps[]
  isDisabled: (value: boolean) => boolean
}

const DAYS = new Array(31).fill(null).map((_, index) => ({ value: index + 1, label: (index + 1).toString() }))
const MONTHS = new Array(12)
  .fill(null)
  .map((_, index) => ({ value: index + 1, label: format(new Date(1970, index), 'LLLL', { locale: ru }) }))
const YEARS = new Array(31).fill(null).map((_, index) => {
  const year = new Date(new Date().getFullYear() - 30 + index, 1).getFullYear()

  return { value: year, label: year.toString() }
})

const isDisabled: IFormValue['isDisabled'] = (value) => !value

const formValues: IFormValue[] = [
  {
    id: 'days',
    label: 'Числа',
    items: DAYS,
    isDisabled,
  },
  {
    id: 'months',
    label: 'Месяцы',
    items: MONTHS,
    isDisabled,
  },
  {
    id: 'years',
    label: 'Годы',
    items: YEARS,
    isDisabled,
  },
]

const maxDate = new Date()
const minDate = new Date()
minDate.setFullYear(minDate.getFullYear() - 30)

interface Props {
  dateRange: DataPickerValue
  handleChangeDate: (value: DataPickerValue) => void
  onCheckboxChange: (key: TDateType, value: boolean) => void
  checkboxValues: Record<TDateType, boolean>
  dateValues: Record<TDateType, number[]>
  onChangeDateValues: (key: TDateType, values: number[]) => void
  className?: string
  error?: string
}

export const CalcPeriodSettings: FC<Props> = ({
  checkboxValues,
  dateRange,
  handleChangeDate,
  onCheckboxChange,
  dateValues,
  onChangeDateValues,
  className,
  error,
}) => {
  return (
    <div className={classNames(cls.form, {}, className ? [className] : undefined)}>
      <div className={cls.datePicker}>
        <p className={cls.datePickerLabel}>Расчетный период</p>

        <div className={cls.datePickerRange}>
          <DateRangePicker
            maxDate={maxDate}
            minDate={minDate}
            dateFrom={dateRange[0]}
            dateTo={dateRange[1]}
            handleChangeDate={handleChangeDate}
            error={error}
          />
        </div>
        <div className={cls.datePickerError}>
          <ErrorExplanationIcon title={error} />
        </div>
      </div>

      {formValues.map((el) => (
        <div key={el.id} className={cls.formItem}>
          <div className={cls.checkbox}>
            <Checkbox
              id={el.id}
              onChange={(_, value) => onCheckboxChange(el.id, value)}
              value={checkboxValues[el.id]}
              disabled={dateRange.some((el) => !el)}
            />
            <label htmlFor={el.id} className={cls.checkboxLabel}>
              {el.label}
            </label>
          </div>

          <Select<string[]>
            variant='outlined'
            disabled={el.isDisabled(checkboxValues[el.id])}
            value={dateValues[el.id]}
            onChange={(values) =>
              onChangeDateValues(
                el.id,
                values.map((el) => Number(el)),
              )
            }
            multiple
            truncateText
            items={el.items}
            MenuProps={{
              PaperProps: {
                style: {
                  maxHeight: 7 * 32 + 8, // 7 элементов по 32px каждый + padding
                },
              },
            }}
            renderValue={(value) => {
              const selectedItems = (value as string[]).map((elem) => {
                const found = el.items.find((item) => item.value === elem)

                return found?.label ?? elem
              })

              return selectedItems.join(', ')
            }}
          />
        </div>
      ))}
    </div>
  )
}
