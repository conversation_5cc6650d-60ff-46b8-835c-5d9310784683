.form {
  display: flex;
  align-items: start;
  gap: 1.5rem;
  height: 150px;

  &Item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 150px;
  }
}

.datePicker {
  display: flex;
  align-items: flex-end;
  padding-top: 25.5px;

  &Label {
    font-weight: 600;
    padding-right: 1rem;
  }

  &Range {
    width: 220px;
  }

  &Error {
    width: 24px;
    & > span {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 26px !important;
    }
  }
}

.checkbox {
  display: flex;

  &Label {
    flex: 1 0 auto;
    margin-left: 0.3rem;
    font-weight: 600;
  }
}

.select {
  &Input {
    display: flex;
    flex-direction: column;
  }
}
