import { ICalcModelRunningTimeTableRow } from 'entities/api/calcModelWerManager.entities'
import { FC } from 'react'
import { classNames } from 'shared/lib/classNames'

import cls from './RunningTimesTableCell.module.scss'

interface Props {
  value: string | number
  row: ICalcModelRunningTimeTableRow
}

export const RunningTimesTableCell: FC<Props> = ({ row, value }) => {
  return (
    <div className={classNames(cls.ellipsisCell, { [cls.readOnlyCell]: !row.editable && row.active })}>{value}</div>
  )
}
