import CloseRoundedIcon from '@mui/icons-material/CloseRounded'
import { observer } from 'mobx-react'
import { FC } from 'react'
import { Button } from 'shared/ui/Button'
import { Modal } from 'shared/ui/Modal'
import { useStore } from 'stores/useStore.ts'

import cls from './ModalCalcProtocol.module.scss'

interface ModalCalcProtocolProps {
  onClose: () => void
}

export const ModalCalcProtocol: FC<ModalCalcProtocolProps> = observer((props) => {
  const { onClose } = props
  const {
    calcModelWerStore: { runningTimesStore },
  } = useStore()
  const { warnings } = runningTimesStore

  return (
    <Modal
      open
      title='Протокол расчёта'
      maxWidth='md'
      skipConfirmOnClose
      onClose={onClose}
      className={cls.modal}
      actions={
        <div className={cls.modalFooterRight}>
          <Button onClick={onClose}>Ок</Button>
        </div>
      }
    >
      <div className={cls.wrapper}>
        <ul className={cls.list}>
          {warnings.map((warning) => (
            <li key={warning} className={cls.listItem}>
              <div className={`${cls.icon} ${cls.iconError}`}>
                <CloseRoundedIcon color='error' width={20} height={20} />
              </div>
              {warning}
            </li>
          ))}
        </ul>
      </div>
    </Modal>
  )
})
