.container {
  padding: 0.2rem;
}

.plants {
  width: 100%;
  display: flex;
  flex-direction: row;
  overflow-x: auto;
}

.group {
  margin: 0 2px;
  padding: 2px;

  &DoesNotHaveAbsent {
    display: none;
  }

  &HasAbsent {
    display: block;
  }

  &Name {
    position: relative;
  }

  &WarningIcon {
    visibility: hidden;
    width: 20px;
    height: 20px;
    margin-left: 0.2rem;

    &Shown {
      visibility: visible;
    }
  }
}

.plant {
  align-items: center;
  display: flex;

  &Hidden {
    display: none;
  }

  &Name {
    flex-grow: 1;
    height: 26px;
    width: auto;
    color: #000;
    font-style: normal;
    margin: 2px;
    padding: 4px;
    border-radius: 4px;
  }

  &IsPlanned {
    color: #000;
  }

  &IsLooked {
    color: #747474;
  }

  &Absent {
    font-style: italic;
    background-color: rgba(#000, 0.05);

    &InTheMiddle {
      color: #d01414;
    }
  }

  &Inactive {
    background-color: rgba(#000, 0.2);
    font-style: italic;
    display: none;

    &Shown {
      display: block;
    }
  }
}

.tooltip {
  // стилизация контейнера Tooltip
  & > div {
    color: #000;
    box-shadow: 0 3px 5px 5px rgba(#000, 0.1);
    font-size: 13px;
    background: #fff;
  }

  &Body {
    display: flex;
    flex-direction: column;
  }

  &Item {
    align-self: self-start;
  }
}

.table {
  &NameCell {
    display: flex;
    align-items: center;
  }

  &PlantsHeaderCell {
    display: flex;
    justify-content: space-between;

    // стилизация якоря для Tooltip
    & > span {
      display: flex;
      align-items: center;
    }
  }
}

.iconTooltip {
  color: var(--orange-color) !important;
  margin: 0 4px;
}
