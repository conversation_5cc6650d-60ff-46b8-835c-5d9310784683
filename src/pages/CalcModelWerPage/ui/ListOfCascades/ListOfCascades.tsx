import HelpOutlineOutlinedIcon from '@mui/icons-material/HelpOutlineOutlined'
import { Tooltip } from '@mui/material'
import { ICalcModelCascadePlantDto, ICalcModelCascadeTableRow } from 'entities/api/calcModelWerManager.entities'
import { observer } from 'mobx-react'
import { useResizeObserver } from 'pages/CalcModelWerPage/hooks/useResizeObserver'
import { useEffect, useMemo, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { Icon } from 'shared/ui/Icon'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { Switch } from 'shared/ui/Switch'
import { useStore } from 'stores/useStore'
import { IColumn, Table } from 'widgets/Table'

import cls from './ListOfCascades.module.scss'
import { HtmlTooltip } from './ui/HtmlTooltip'

const getClassName = (value: ICalcModelCascadePlantDto, isLastOrFirst: boolean, activeOnly: boolean) => {
  // приоритет поля active выше остальных полей, поэтому по остальным флагам не отображаем
  if (!value.active) {
    return classNames(cls.plantInactive, { [cls.plantInactiveShown]: !activeOnly })
  }
  // приоритет поля isPlanned выше остальных полей, поэтому по остальным флагам не отображаем
  if (value.isPlanned) return cls.plantIsPlanned
  if (!value.isPlanned && value.isLooked) return cls.plantIsLooked
  if (!value.isPlanned && !value.isLooked) {
    if (isLastOrFirst) return cls.plantAbsent

    return classNames(cls.plantAbsent, {}, [cls.plantAbsentInTheMiddle])
  }

  return ''
}

const promptTooltipItems = [
  {
    className: classNames(cls.tooltipItem, {}, [cls.plantName, cls.plantIsLooked]),
    title: 'станция для просмотра',
  },
  { className: classNames(cls.tooltipItem, {}, [cls.plantName, cls.plantIsPlanned]), title: 'станция ДЦ (ВЭР)' },
  {
    className: classNames(cls.tooltipItem, {}, [cls.plantName, cls.plantAbsent]),
    title: 'станция, отсутствующая в РМ, в конце или начале списка',
  },
  {
    className: classNames(cls.tooltipItem, {}, [cls.plantName, cls.plantAbsent, cls.plantAbsentInTheMiddle]),
    title: 'станция, отсутствующая в РМ, в середине списка',
  },
  {
    className: classNames(cls.tooltipItem, {}, [cls.plantName, cls.plantInactive, cls.plantInactiveShown]),
    title: 'недействующая станция',
  },
]

// общая высота других элементов на вкладке
const HEIGHT_OF_OTHER_ELEMENTS_ON_PAGE = 138

export const ListOfCascades = observer(() => {
  const {
    calcModelWerStore: { listOfCascadesStore },
  } = useStore()
  const [activeOnly, setActiveOnly] = useState<boolean>(true)
  const height = useResizeObserver(HEIGHT_OF_OTHER_ELEMENTS_ON_PAGE)

  const onChangeActiveOnly = (value: boolean) => {
    setActiveOnly(value)
    listOfCascadesStore.getListOfCascades(value)
  }

  const columns: IColumn[] = useMemo(
    () => [
      {
        name: 'name',
        title: 'Название',
        width: 450,
        editingEnabled: false,
        render: (value, row: ICalcModelCascadeTableRow) => {
          const showError = row.plants.length > 1

          return (
            <div className={cls.tableNameCell}>
              {showError && (
                <Tooltip placement='right-start' title='Ошибка формирования каскада: присутствуют несвязанные станции'>
                  <span className={cls.iconTooltip}>
                    <Icon name='information' width={13} />
                  </span>
                </Tooltip>
              )}
              <span>{value}</span>
            </div>
          )
        },
      },
      {
        name: 'plants',
        title: 'Состав',
        width: 600,
        editingEnabled: false,
        render: (value: ICalcModelCascadePlantDto[][], row: ICalcModelCascadeTableRow) => {
          return (
            <div className={cls.plants}>
              {value.map((group, index) => {
                const plantsActiveOnly = group.filter((el) => el.active)
                const absentPlantInTheMiddleOfGroup = plantsActiveOnly.find(
                  (el, idx) => !el.isLooked && !el.isPlanned && idx !== 0 && idx !== plantsActiveOnly.length - 1,
                )

                return (
                  <div key={`${row.id}-${index.toString()}`} className={cls.group}>
                    {group.map((el) => {
                      const elIndex = plantsActiveOnly.findIndex((item) => el.plantId === item.plantId)
                      const isCurrentItemAbsentInTheMiddle =
                        elIndex !== 0 && elIndex !== plantsActiveOnly.length - 1 && !el.isLooked && !el.isPlanned
                      const className = getClassName(el, !isCurrentItemAbsentInTheMiddle, activeOnly)

                      return (
                        <div
                          key={`${el.plantId}-${el.name}`}
                          className={classNames(cls.plant, { [cls.plantHidden]: !el.active && activeOnly })}
                        >
                          <div className={classNames(cls.plantName, {}, [className])}>{el.name}</div>

                          <Tooltip
                            title='Станция в середине каскада отсутствует в РМ. Расчет по каскаду в таком случае невозможен'
                            placement='right-start'
                          >
                            <div
                              className={classNames(cls.groupWarningIcon, {
                                [cls.groupDoesNotHaveAbsent]: !absentPlantInTheMiddleOfGroup,
                                [cls.groupHasAbsent]: !!absentPlantInTheMiddleOfGroup,
                                [cls.groupWarningIconShown]: isCurrentItemAbsentInTheMiddle,
                              })}
                            >
                              <Icon name='exclamationTriangle' width={18} />
                            </div>
                          </Tooltip>
                        </div>
                      )
                    })}
                  </div>
                )
              })}
            </div>
          )
        },
        headRender: (value) => (
          <div className={cls.tablePlantsHeaderCell}>
            {value}
            <HtmlTooltip
              placement='right-start'
              className={cls.tooltip}
              title={
                <div className={cls.tooltipBody}>
                  {promptTooltipItems.map((item) => (
                    <div className={item.className} key={item.title}>
                      {item.title}
                    </div>
                  ))}
                </div>
              }
            >
              <span>
                <HelpOutlineOutlinedIcon />
              </span>
            </HtmlTooltip>
          </div>
        ),
      },
    ],
    [activeOnly],
  )

  useEffect(() => {
    listOfCascadesStore.getListOfCascades(activeOnly)
  }, [])

  return (
    <div className={cls.container}>
      <SubtitleWithActions
        title='Перечень каскадов'
        actions={[
          <Switch
            key={1}
            label='Только действующие'
            checked={activeOnly}
            onChange={(_, value) => onChangeActiveOnly(value)}
          />,
        ]}
        isActionsVisible
      />

      <div className={cls.table}>
        <Table
          rows={listOfCascadesStore.listOfCascades}
          columns={columns}
          height={height}
          columnSearchDisabled={['plants']}
          loading={listOfCascadesStore.loading}
        />
      </div>
    </div>
  )
})
