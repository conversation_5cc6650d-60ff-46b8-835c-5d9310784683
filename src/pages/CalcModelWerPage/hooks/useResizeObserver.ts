import { useCallback, useEffect, useMemo, useState } from 'react'

export const useResizeObserver = (subtrahend: number) => {
  const [y, setY] = useState(document.documentElement.clientHeight - subtrahend)

  const onResize = useCallback(() => {
    setY(document.documentElement.clientHeight - subtrahend)
  }, [subtrahend])

  const observer = useMemo(() => new ResizeObserver(onResize), [onResize])

  useEffect(() => {
    observer.observe(document.body)

    return () => {
      observer.disconnect()
    }
  }, [onResize])

  return y
}
