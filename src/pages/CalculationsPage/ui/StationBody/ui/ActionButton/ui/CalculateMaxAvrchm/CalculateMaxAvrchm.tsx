import { StageType } from 'entities/api/calculationsManager.entities'
import { PlanningStage } from 'entities/shared/common.entities'
import { MessagesWarnings } from 'entities/widgets/Vault.entities.ts'
import { observer } from 'mobx-react'
import { CalculationActionButton } from 'pages/CalculationsPage/ui/CalculationButton'
import { getPrepareDate } from 'pages/CalculationsPage/ui/StationBody/lib'
import { useStore } from 'stores/useStore'

import { calculateMaxAvrchm } from './api'

interface CalculateMaxAvrchmProps {
  isEditRows: boolean
  selectLeftMenu: number
  selectedStage: PlanningStage | string
  actualStage: StageType
  initDate: () => Date
  onCalculationComplete: () => void
}

export const CalculateMaxAvrchm = observer((props: CalculateMaxAvrchmProps) => {
  const { isEditRows, selectLeftMenu, selectedStage, actualStage, initDate, onCalculationComplete } = props

  const { calculationsPageStore, godModeStore } = useStore()
  const { isLastDay, viewOnly, editMode } = calculationsPageStore
  const { godMode } = godModeStore

  const handleCalculateMaxAvrchm = async () => {
    const prepareDate = getPrepareDate(initDate())
    const stage = selectedStage === 'ACTUAL' ? actualStage.code : selectedStage

    try {
      await calculateMaxAvrchm({
        plantId: selectLeftMenu,
        targetDate: prepareDate,
        planingStage: stage,
        godMode,
      })

      onCalculationComplete()

      calculationsPageStore.rootStore.notificationStore.addNotification({
        title: 'Расчёт АВРЧМ по максимуму',
        description: 'Расчёт АВРЧМ по максимуму выполнен успешно',
        type: 'success',
      })
    } catch (error) {
      console.error(error)

      calculationsPageStore.rootStore.notificationStore.addNotification({
        title: 'Расчёт АВРЧМ по максимуму',
        description: 'Произошла ошибка при расчёте АВРЧМ по максимуму',
        type: 'error',
      })
    }
  }

  return (
    <CalculationActionButton
      buttonName={MessagesWarnings.CALCULATE_MAX_AVRCHM}
      viewOnly={viewOnly}
      isEditRows={isEditRows}
      isLastDay={isLastDay}
      editMode={editMode}
      onClick={handleCalculateMaxAvrchm}
    />
  )
})
