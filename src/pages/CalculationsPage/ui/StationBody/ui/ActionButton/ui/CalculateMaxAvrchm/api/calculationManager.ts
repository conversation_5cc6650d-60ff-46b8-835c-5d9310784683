import { PlanningStage } from 'entities/shared/common.entities.ts'
import { axiosInstance as api } from 'shared/lib/axios'

export interface CalculateMaxAvrchmRequestDto {
  plantId: number
  targetDate: string
  planingStage: PlanningStage | string
  godMode?: boolean
}

/**
 * Рассчитать АВРЧМ по максимуму (по значению Rmax)
 */
export const calculateMaxAvrchm = (payload: CalculateMaxAvrchmRequestDto): Promise<void> => {
  return api.post('/api/v1/calculation/max-avrchm', payload)
}
