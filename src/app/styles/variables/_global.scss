:root {
  --font-family-main: 'SF Pro Display', sans-serif;
  --font-size-m: 14px;
  --font-line-m: 24px;
  --font-m: var(--font-size-m) / var(--font-line-m) var(--font-family-main);
  --font-size-l: 24px;
  --font-line-l: 32px;
  --font-l: var(--font-size-l) / var(--font-line-l) var(--font-family-main);
  --shadow-page: 0 2px 3px 0 rgb(0 0 0 / 5%), 0 1px 2px 0 rgb(0 0 0 / 10%);

  // Размеры
  --navbar-height: 40px;
  --sidebar-width: 208px;
  --sidebar-width-collapsed: 56px;
  --header-page: 38px;
  --row-color-gray: #8080801f;
  --row-height: 20px;
  --row-select-height: 32px;

  font-size: 16px;

  @media (width <= 1450px) {
    --sidebar-width: 220px;
  }
}

:-webkit-autofill {
  box-shadow: 0 0 0 1000px #fff inset !important;
  -webkit-text-fill-color: #000;
}

::-webkit-scrollbar {
  width: 9px; /* ширина scrollbar */
  height: 9px;
}

::-webkit-scrollbar-track {
  background: #fff; /* цвет дорожки */
}

::-webkit-scrollbar-thumb {
  background-color: gray; /* цвет плашки */
  border-radius: 7px; /* закругления плашки */
  width: 7px;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  box-shadow: 0 0 0 30px white inset !important;
  background-color: white !important;
}

.ant-dropdown-menu-item-selected {
  background-color: #e6f4ff;
  color: var(--primary-color) !important;
}

.ant-tabs-tab {
  padding: 12px 8px !important;
}

.MuiTabs-indicator {
  background-color: var(--primary-color) !important;
}

.MuiInput-root::after {
  border-bottom: 2px solid var(--primary-color) !important;
}

.ResizingControl-resizeHandleLine {
  background-color: var(--primary-color) !important;
}

.Mui-checked {
  color: var(--primary-color);
  .MuiSwitch-thumb {
    color: var(--primary-color);
  }
}

.Mui-checked + .MuiSwitch-track {
  background-color: var(--primary-color) !important;
}

.MuiFormControlLabel-label {
  color: var(--text-color);
  text-align: center !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: normal !important;
  line-height: normal !important;
  font-family: var(--font-family-main) !important;
}

.MuiTableCell-root {
  font-family: var(--font-family-main) !important;
  padding: 0 4px !important;
  user-select: none !important;
}

input {
  font-family: var(--font-family-main) !important;
}

.MuiTableCell-head {
  border-bottom: none !important;
}

.MuiSelect-select {
  display: flex !important;
}

.MuiPopover-root {
  font: var(--font-m) !important;
  color: var(--primary-color) !important;
}

.MuiSelect-selected {
  background-color: var(--primary-color) !important;
}

.MuiMenuItem-gutters {
  height: var(--row-select-height);

  &:hover {
    background-color: var(--primary-color) !important;
    color: var(--primary-color-invert) !important;
  }
}

.MuiSvgIcon-root {
  width: 20px !important;
  height: 20px !important;
}

.MuiInput-root:focus {
  background-color: transparent !important;
}

.Mui-selected {
  background-color: var(--primary-color) !important;
  color: var(--primary-color-invert) !important;
  transition: all 0.3s;
}

.MuiTooltip-popper {
  z-index: 1302;
}

.TableFixedCell-fixedCell {
  & > div {
    border-right: none !important;
  }
}

.HandsontableCopyPaste {
  display: none;
}

.MuiTab-root,
.MuiTabs-root {
  min-height: 38px !important;
}

.MuiTab-root {
  padding: 10px 16px !important;
}

.MuiDialogContent-root,
.MuiDialogTitle-root {
  padding: 14px !important;
}

.MuiCheckbox-root {
  width: 20px;
  max-width: 20px;
  min-width: 20px;
  height: 20px;
  max-height: 20px;
  min-height: 20px;
  margin: auto !important;

  &.Mui-checked {
    color: var(--primary-color) !important;
  }
}

.highcharts-tooltip-container {
  z-index: 10 !important;
  padding: 0 !important;
  height: 40px;
  overflow: hidden;
}

.MuiDialogActions-spacing {
  padding: 0 !important;
}

// Стилизация уведомлений (ToastProvider)
.MuiSnackbar-root .MuiAlert-root {
  box-shadow:
    rgb(0 0 0 / 20%) 0 3px 5px -1px,
    rgb(0 0 0 / 14%) 0 6px 10px 0,
    rgb(0 0 0 / 12%) 0 1px 18px 0;
  border-radius: 8px;
}

.MuiInputBase-root.MuiOutlinedInput-root {
  &.Mui-focused {
    .MuiOutlinedInput-notchedOutline {
      border-color: var(--primary-color);
    }
  }
  &:hover {
    .MuiOutlinedInput-notchedOutline {
      border-color: var(--primary-color);
    }
  }
}
