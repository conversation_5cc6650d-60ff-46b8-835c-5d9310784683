:root {
  --disabled-button-bg: #0000000a;
  --text-color: #000;
  --green-color: #394;
  --red-color: #d01414;
  --red-dark-color: #b81414;
  --orange-color: #e9b738;
  --blue-color: #0263d9;
  --blue-light-color: #4f7ed9;
  --violet-color: #a759e4;
  --violet-dark-color: #28178e;
  --background-color-primary: #fafafa;
  --background-color-secondary: #fff;
  --text-gray: #747474;
  --green-background: #bdebc4;
  --red-background: #fdcdcd;
  --blue-background: #bfdcff;
  --yellow-background: #ffeec1;
  --gray-background: #e0e0e0;
  --yellow-color: #ffd159;
  --light-gray: #e0e0e0;

  // Цвет для режима бога
  --gold-color: #ffd700;

  // Цвет шрифта ВСВГО для поля «актуальный этап» в боковом меню, на странице Расчетов
  --color-actual-stage-vsvgo: #16781c;
  // Цвет фона ВСВГО для поля «актуальный этап» в боковом меню, на странице Расчетов
  --bg-color-actual-stage-vsvgo: #d2f4db;

  // Цвет этапов: ПЭР и ПДГ
  --color-stage-rsv: #0263d9;
  // Цвет этапов: ВСВГО
  --color-stage-vsvgo: #394;

  // Цвет акцепта
  --bg-color-accept: #09eb9857;

  // Цвет при несоответствии этапа планирования в таблице
  --color-planning-stages-do-not-match: #a61c1c;

  // Цвет фона шапки
  --spreadsheet-bg-color-th: rgba(224, 224, 224, 1);

  // Цвет границ
  --spreadsheet-border-color: #ccc;
  // Темный цвет для границ
  --spreadsheet-border-color-dark: #3f3e3e;

  // Цвет фона невалидных данных
  --spreadsheet-bg-color-error: #f79f9f;

  // Цвет шрифта невалидных данных
  --spreadsheet-color-error: #a61c1c;

  // Цвет фона данных не доступных для редактирования
  --spreadsheet-color-disabled: rgba(240, 240, 240, 1);

  // Цвет фона шапки Итог(план) для оптимизируемых ГЭС
  --spreadsheet-bg-color-th-optimized-plant: rgba(224, 219, 254);
  // Цвет фона значений Итог(план) для оптимизируемых ГЭС
  --spreadsheet-bg-color-td-optimized-plant: rgba(224, 219, 254, 0.6);
  // Цвет фона шапки Итог (план) для оптимизируемых РГЕ ГЭС
  --spreadsheet-bg-color-th-optimized-rgu: rgba(224, 219, 254, 0.5);
  // Цвет фона значений Итог (план) для оптимизируемых РГЕ ГЭС
  --spreadsheet-bg-color-td-optimized-rgu: rgba(224, 219, 254, 0.4);

  // Цвет фона шапки Итог(план) для не оптимизируемых ГЭС
  --spreadsheet-bg-color-th-not-optimized-plant: rgba(255, 253, 199, 1);
  // Цвет фона значений Итог(план) для не оптимизируемых ГЭС
  --spreadsheet-bg-color-td-not-optimized-plant: rgba(255, 253, 199, 0.6);
  // Цвет фона шапки Итог (план) для не оптимизируемых РГЕ ГЭС
  --spreadsheet-bg-color-th-not-optimized-rgu: rgba(255, 253, 199, 0.5);
  // Цвет фона значений Итог (план) для не оптимизируемых РГЕ ГЭС
  --spreadsheet-bg-color-td-not-optimized-rgu: rgba(255, 253, 199, 0.4);

  // Цвет фона шапки Итог.огр ГЭС
  --spreadsheet-bg-color-th-limit-plant: rgba(164, 201, 246, 1);
  // Цвет фона значений Итог.огр ГЭС
  --spreadsheet-bg-color-td-limit-plant: rgba(164, 201, 246, 0.6);
  // Цвет фона шапки Итог.огр для РГЕ ГЭС
  --spreadsheet-bg-color-th-limit-plant-rgu: rgba(164, 201, 246, 0.5);
  // Цвет фона значений Итог.огр РГЕ ГЭС
  --spreadsheet-bg-color-td-limit-plant-rgu: rgba(164, 201, 246, 0.4);

  // Цвет фона шапки Модес ГЭС
  --spreadsheet-bg-color-th-modes-plant: rgba(195, 195, 195, 1);
  // Цвет фона значений Модес ГЭС
  --spreadsheet-bg-color-td-modes-plant: var(--spreadsheet-color-disabled);
  // Цвет фона шапки Модес для РГЕ ГЭС
  --spreadsheet-bg-color-th-modes-plant-rgu: rgba(195, 195, 195, 0.5);

  // Цвет штриховки заблокированной валидной ячейки Эмин
  --spreadsheet-bg-hatching-valid-color: rgba(128, 0, 128, 0.1);
  // Цвет штриховки заблокированной невалидной ячейки Эмин
  --spreadsheet-bg-hatching-error-color: rgba(166, 28, 28, 0.2);
}
