@use './all' as *;

body {
  font: var(--font-m);
}

h2 {
  font-size: 1.13rem;
}

.app {
  background-color: var(--background-color-primary);
  color: var(--primary-color);
  min-height: 100vh;
  position: relative;
}

.content-page {
  display: flex;
  width: 100vw;
  height: calc(100vh - var(--navbar-height));
  overflow: hidden;
}

.page-wrapper {
  width: 50%;
  flex-grow: 1;
  padding: 0.2rem;
  z-index: 5;
}

.god-mode_bg-color {
  background-color: var(--gold-color) !important;
  transition: background-color 0.2s ease;
}

.god-mode_color {
  color: var(--gold-color) !important;
  transition: color 0.2s ease;
}

span {
  &:hover {
    border-color: var(--primary-color) !important;
  }
}

.ant-select-selector {
  &:hover {
    border-color: var(--primary-color) !important;
  }
}

.ant-select-focused:where(.css-dev-only-do-not-override-1wazalj).ant-select:not(
    .ant-select-disabled,
    .ant-select-customize-input,
    .ant-pagination-size-changer
  )
  .ant-select-selector {
  border-color: var(--primary-color) !important;

  &:hover {
    border-color: var(--primary-color) !important;
  }
}

.ant-input-affix-wrapper-focused {
  border-color: var(--primary-color) !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  background: red;
}

.MuiDateRangeCalendar-root > div:first-child {
  display: none;
}

.MuiMenuItem-root {
  font-size: 0.875rem !important;
  font-family: var(--font-family-main) !important;
}

.MuiFormHelperText-root {
  font-family: var(--font-family-main) !important;
}

div[class$='MuiTypography-root-MuiDialogTitle-root'],
.MuiDialogTitle-root {
  font-size: 1.125rem !important;
  font-family: var(--font-family-main) !important;
  font-weight: 600;
}

div[class$='MuiDialogContent-root'] {
  font-family: var(--font-family-main) !important;
}

th.CellLayout-cell {
  font-weight: 700;
}

td.MuiTableCell-body {
  & .MuiCheckbox-root {
    padding: 0;
  }
}

.MuiTooltip-tooltip {
  line-height: 1.5;
}

input.MuiOutlinedInput-input {
  padding: 0.25em 1em;
  border-radius: 6px;
  font-size: 0.75rem !important;
  font-weight: 600;
}

.MuiInputBase-multiline {
  padding: 0.25em 1em !important;
  font-family: var(--font-family-main) !important;
  font-size: 0.75rem !important;
  font-weight: 600;
}

td.MuiTableell-root:first-child .MuiCheckbox-root {
  width: 100%;
}

.Mui-error {
  color: var(--red-color) !important;
  border-color: var(--red-color) !important;
}

.legend-with-border {
  .highcharts-legend-item > rect {
    stroke: #ffae00;
  }
}

.MuiChip-label {
  font-family: var(--font-family-main);
  font-size: 0.75rem;
}
