.stackToast {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: end;
  overflow: auto;
  padding: 16px;
  border-radius: 8px;

  &::-webkit-scrollbar {
    display: none;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  &Wrapper {
    height: 100%;
    position: fixed;
    inset: 0;
    z-index: 1400;
    padding-top: 40px; // Высота шапки

    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: end;
    pointer-events: none;
  }
}

.closeAllButton {
  margin-right: 16px;
  pointer-events: auto;
  color: var(--primary-color);
}
