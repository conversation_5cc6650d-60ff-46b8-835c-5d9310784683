.snackbar {
  position: relative;
  bottom: unset;
  left: unset;
  right: unset;
  pointer-events: auto;
}

.buttonContainer {
  margin-top: 6px;
  width: 100%;
}

.updateButton {
  background-color: #0263d9 !important;
  margin: 0 4px !important;
}
.ignoreButton {
  background-color: transparent !important;
  border: solid 1px #000000 !important;
  color: #000000 !important;
  margin: 0 4px !important;
}

.multiErrorWrapper {
  max-height: 60px;
  padding-right: 10px;
  overflow-y: auto;
}

.multiErrorWrapper::-webkit-scrollbar-track {
  background: transparent !important; /* цвет дорожки */
}
