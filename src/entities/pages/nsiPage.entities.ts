import { IPlanDepartment } from 'entities/api/nsiManager.entities.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { IUserDetails } from 'shared/api/userManager/userManager'
import { IconNameProps } from 'shared/ui/Icon/Icon.type'
import { ItemsProps } from 'widgets/Tabs/Tabs'

const commonSettings = {
  icon: 'book' as IconNameProps,
  rules: [ROLES.ADMIN, ROLES.TECHNOLOGIST, ROLES.TECH_ADMIN_CM, ROLES.TECH_ADMIN_NSI, ROLES.GUEST],
  isView: true,
}

export const tabsNsi: ItemsProps[] = [
  { key: 'listofobjects', label: 'Перечень объектов', ...commonSettings },
  { key: 'listofterritories', label: 'Перечень территорий', ...commonSettings },
  { key: 'listofrestrictions', label: 'Перечень ограничений', ...commonSettings },
  { key: 'listofcascades', label: 'Перечень каскадов', ...commonSettings },
  { key: 'referencedata', label: 'Справочные данные', ...commonSettings },
  { key: 'indicators', label: 'Показатели', ...commonSettings },
]

export type THierarchyType = 'BY_RGU' | 'BY_GENERATOR'

export type TDepartemntsVer = { name: string }[]

export interface ListOfObjectsProps {
  height?: number
  userDetail: IUserDetails
}

export interface IRowListOfObject {
  tabId: string
  startDate: string
  endDate: string
  type: string
  children?: IRowListOfObject[]
  isEdit?: boolean
  id: number | null
  marketCalcModelId: number | null
  name: string
  active: boolean
  planDepartments: IPlanDepartment[]
  tempEdits?: string[]
  werDepartments?: { value: number }[]
}

export interface IResPrepareDataBeforeSave {
  type?: string
  id?: number | null
  startDate: string | null
  endDate: string | null
  planDepartmentIds: string[] | null
  marketCalcModelId?: number | null
}

export interface IRowDepartmentsVer {
  label: string
  name: string
  value?: number
  children?: IRowDepartmentsVer[]
}

export const KEY_TABLE = `list-of-objects`

export const getIcon = (type: string): 'plant' | 'rge' | 'generator' | 'department' => {
  if (type === 'PLANT') {
    return 'plant'
  }
  if (type === 'RGU') {
    return 'rge'
  }
  if (type === 'RGU_RELATION') {
    return 'rge'
  }
  if (type === 'GENERATOR') {
    return 'generator'
  }

  return 'department'
}

interface IReferenceDataCol {
  title: string
  freeze?: boolean
  search?: boolean
}

export interface IReferenceDataCell {
  value?: string | number
  todayChanged: boolean
  previousChanged: boolean
}

interface IReferenceDataRow {
  cells: IReferenceDataCell[]
  active: boolean
}

export interface IGetReferenceDataParams {
  date: string
}

export interface IReferenceData {
  previousDate?: string
  nextDate?: string
  currentDate?: string
  table: {
    columns: IReferenceDataCol[]
    rows: IReferenceDataRow[]
  }
}

export type TReferenceDataTableRow = Record<string, IReferenceDataCell | string> & {
  tabId: string
  rowColor?: string | null
}

export type TIndicatorDto = {
  id: number
  code: number
  shortName: string
  fullName: string
  measurementUnit: string
  precision?: number
  comment?: string
}

export interface IIndicatorObjDto {
  id: number
  title: string
  indicators: TIndicatorDto[]
}

export type TIndicatorChild = TIndicatorDto & {
  tabId: string
  fullPath: string
  parentId: number
  edited: boolean
}

export type TIndicatorItem = TIndicatorDto & {
  title: string
  tabId: string
  fullPath: string
  children: TIndicatorChild[]
  edited?: boolean
}

export interface IIndicatorUpdateParams {
  id: number
  precision?: number
  comment?: string
}
