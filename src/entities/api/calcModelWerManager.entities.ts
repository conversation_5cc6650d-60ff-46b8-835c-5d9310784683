import { IEnrichedTabIdData } from 'entities/shared/common.entities'
import { TDateType } from 'pages/CalcModelWerPage/ui/RunningTimes/lib/constants'
import { DataPickerValue } from 'shared/ui/DateRangePicker/ui/DateRangePicker'
import { IPlant } from 'stores/CalcModelStore/ListOfStationsStore/ListOfStationsStore.types'

export interface IWerDepartment {
  id: number
  uid: string
  name: string
  departmentLevel: 'CDU' | 'ODU' | 'RDU'
  parentId?: number
}

export interface IPlantRelation {
  id?: number
  plantId: number
  plantName: string
  werDepartments: IWerDepartment[]
  startDate: string
  endDate?: string
  affluent?: boolean
}

interface ICascade {
  id: number
  name: string
  archived: boolean
}

export interface IPlantReferenceData {
  plantId: number
  parameterName: string
  description: string
  startDate: string
  measUnit?: string
  parameterValue: {
    type: string
    value?: string | number
    manual: boolean
    fixed: boolean
  }
}

export interface IPlantCascades {
  plantId: number
  cascades: ICascade[]
  upstreamPlants: IPlantRelation[]
  downstreamPlants: IPlantRelation[]
}

interface ICascadePlant {
  plantId: number
  name: string
  active: boolean
}

type TPlantList = ICascadePlant[][]

export type IDownstreamPlant = Omit<ICascadePlant, 'active'>

export interface ICascadeWithPlants {
  id: number
  name: string
  archived: boolean
  plants: TPlantList
  allowArchive: boolean
  allowDelete: boolean
}

type PolynomValue = number | string | null

// Настройки валидации для включения/отключения правил
export interface IValidationSettings {
  emptyValuesValidation?: boolean
  allowableChangeValidation?: boolean
  monotonyValidation?: boolean
}

export interface IReservoirVolumeSettings {
  rowCount: number | string
  unit: 'KM3' | 'MLN_M3'
  levelAllowableChange?: number | string
  volumeAllowableChange?: number | string
  emptyValuesValidation: boolean
  allowableChangeValidation: boolean
  monotonyValidation: boolean
  polynom: PolynomValue[]
  method: 'TABLE' | 'POLYNOM'
}

export interface ITailraceSettings {
  rowCount: number | string
  downstreamPlant?: IDownstreamPlant
  downstreamPlantLevels?: (number | string)[]
  freezingWatch: boolean
  freezingBegin: string
  freezingEnd: string
  consumptionAllowableChange?: number | string
  tailraceAllowableChange?: number | string
  emptyValuesValidation: boolean
  allowableChangeValidation: boolean
  monotonyValidation: boolean
  polynom: PolynomValue[]
  method: 'TABLE' | 'POLYNOM'
}

export interface ISpecificConsumptionSettings {
  rowCount: number | string
  source: 'PRESSURE' | 'HEADRACE_LEVEL'
  unit: 'M3_S_MW' | 'M3_S_MLN_KWH' | 'M3_MW' | 'M3_MLN_KWH'
  pressureAllowableChange?: number | string
  consumptionAllowableChange?: number | string
  emptyValuesValidation: boolean
  allowableChangeValidation: boolean
  monotonyValidation: boolean
  polynom: PolynomValue[]
  method: 'TABLE' | 'POLYNOM'
}

export type CharacteristicsSpreadsheetCell = {
  value?: string | number | null
}

interface ICharacteristicsTable {
  plant: {
    plantId: number
    name: string
  }
  columns: {
    title: string
  }[]
  rows: {
    title: string
    cells: CharacteristicsSpreadsheetCell[]
  }[]
}

interface ICharacteristicsTableWithSettings<TCharacteristicsSettings> {
  settings: TCharacteristicsSettings
  table: ICharacteristicsTable
  lastFilledRow: number
  freezingLastFilledRow?: number
  freezingTable?: ICharacteristicsTable
}

export type IReservoirVolumeTableWithSettingsResponse = ICharacteristicsTableWithSettings<IReservoirVolumeSettings>
export type ISpecificConsumptionTableWithSettingsResponse =
  ICharacteristicsTableWithSettings<ISpecificConsumptionSettings>

export interface IReservoirVolumeVbLevelIndicatorDto {
  id: number
  displayName: string
}

export interface IReservoirVolumeVbLevelIndicatorsResponse {
  indicators: IReservoirVolumeVbLevelIndicatorDto[]
  selectedIndicator: IReservoirVolumeVbLevelIndicatorDto
}

export type ITailraceTableWithSettingsResponse = ICharacteristicsTableWithSettings<ITailraceSettings>

export type CharacteristicsSpreadsheetChanges = {
  matrix: (number | null | string)[][]
  downstreamPlantId?: number
  freezing?: boolean
}

export interface ICalcModelCascadePlantDto {
  plantId: number
  name: string
  active: boolean
  isPlanned: boolean
  isLooked: boolean
}

export interface ICalcModelCascadeDto {
  id: number
  name: string
  archived: boolean
  allowArchive: boolean
  allowDelete: boolean
  plants: ICalcModelCascadePlantDto[][]
}

export type ICalcModelCascadeTableRow = IEnrichedTabIdData<ICalcModelCascadeDto>

export type ICalcModelRunningTimeItem = {
  id: number
  beginDate: string
  stopDate: string
  normTime: number
  masterTime: number | null
  calculatedTime: number | null
  comment?: string
  minTime: number | null
  maxTime: number | null
  gridStep: number | null
  accuracy: number | null
}

export interface ICalcModelPlantRunningTimesDto {
  id: number
  upstreamPlant: IPlant
  downstreamPlant: IPlant
  startDate?: string
  endDate?: string
  editable: boolean
  active: boolean
  runningTimes: ICalcModelRunningTimeItem[]
}

export interface ICalcModelPlantRunningTimesItem extends ICalcModelPlantRunningTimesDto {
  /** состояние для обозначения записи как отредактированной */
  edited?: boolean
}

export interface ICalcModelRunningTimeTableRow {
  id: number
  bond: string
  rowSpan: number
  period: string
  editable: boolean
  active: boolean
  tabId: string
  runningTimeBeginDate: string | null
  runningTimeStopDate: string | null
  runningTimeNormTime: number | null
  runningTimeMasterTime: number | null
  runningTimeCalcTime: number | null
  runningTimeComment: string | null
  runningTimeMinTime: number | null
  runningTimeMaxTime: number | null
  runningTimeGridStep: number | null
  runningTimeAccuracy: number | null
  upstreamPlant: IPlant
  downstreamPlant: IPlant
  isEmptyRow?: boolean
  rowColor?: string
  enableShiftTimeButton: boolean
  plantId: number
  runningTimeId: number | null
}

export interface IAddEditRunningTimeForm {
  runningTimeBeginDate: Date | null
  runningTimeStopDate: Date | null
  runningTimeNormTime: number | null
  runningTimeMasterTime: number | null
  runningTimeCalcTime: number | null
  runningTimeComment: string | null
}

export interface IAddEditRunningTimeCalcForm {
  dateRange: DataPickerValue
  checkboxDateValues: Record<TDateType, boolean>
  dateValues: Record<TDateType, number[]>
  availableRangeMinValue: number | string | null
  availableRangeMaxValue: number | string | null
  calcStep: number | string | null
  precision: number | string | null
}

export type IAddEditRunningTimeCalcFormValidationKeys =
  | keyof Omit<IAddEditRunningTimeCalcForm, 'availableRangeMaxValue' | 'availableRangeMinValue'>
  | 'availableRange'

export interface ICalcModelPlantPostRunningTimesParams {
  id: number
  runningTimes: (Omit<ICalcModelRunningTimeItem, 'id'> & { id?: number })[]
}

export interface ICalcAllRunningTimesParams {
  periodBegin: string
  periodEnd: string
  days: number[]
  months: number[]
  years: number[]
}

export interface ICalcRunningTimeSingleDto {
  runningTimeId: number
  calculatedTime: number
}
export interface ICalcRunningTimesResultDto {
  plantStreamId: number
  runningTimes: ICalcRunningTimeSingleDto[]
}

export interface ICalcRunningTimeResultsDto {
  warnings: string[]
  results: ICalcRunningTimesResultDto[]
}

export interface ICalcSingleRunningTimeResultDto {
  year: number
  discrepancy: number
  runningTime: number
}

export interface ICalcSingleRunningTimeParams extends ICalcAllRunningTimesParams {
  plantStreamId: number
  beginDate: string
  stopDate: string
  minTime: number
  maxTime: number
  gridStep: number
  accuracy: number
}
